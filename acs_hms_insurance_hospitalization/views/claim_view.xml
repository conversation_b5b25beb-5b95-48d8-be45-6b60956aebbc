<?xml version="1.0"?>
<odoo>

    <!-- Claim View -->
    <record id="view_claim_tree" model="ir.ui.view">
        <field name="name">insurance.claim.tree</field>
        <field name="model">hms.insurance.claim</field>
        <field name="inherit_id" ref="acs_hms_insurance.view_claim_tree"/>
        <field name="arch" type="xml">
            <field name="appointment_id" position="before">
                <field name="hospitalization_id"/>
            </field>
        </field>
    </record>

    <record id="view_insurance_claim_form" model="ir.ui.view">
        <field name="name">hms.insurance.claim.form</field>
        <field name="model">hms.insurance.claim</field>
        <field name="inherit_id" ref="acs_hms_insurance.view_insurance_claim_form"/>
        <field name="arch" type="xml">
            <field name="claim_for" position="after">
                <field name="hospitalization_id" domain="[('patient_id', '=', patient_id)]" options="{'no_create': True}" attrs="{'required': [('claim_for', '=', 'hospitalization')], 'invisible': [('claim_for', '!=', 'hospitalization')]}"/>
            </field>
        </field>
    </record>

    <record id="view_acs_insurance_claim_calendar" model="ir.ui.view">
        <field name="name">hms.insurance.claim.calendar</field>
        <field name="model">hms.insurance.claim</field>
        <field name="inherit_id" ref="acs_hms_insurance.view_acs_insurance_claim_calendar"/>
        <field name="type">calendar</field>
        <field name="arch" type="xml">
            <field name="appointment_id" position="before">
                <field name="hospitalization_id"/>
            </field>
        </field>
    </record>

</odoo>