<?xml version="1.0"?>
<odoo>

    <!-- Hospitalization -->
    <record id="view_acs_hospitalization_form" model="ir.ui.view">
        <field name="name">view.hospitalization.claim.form</field>
        <field name="model">acs.hospitalization</field>
        <field name="inherit_id" ref="acs_hms_hospitalization.view_acs_hospitalization_form"/>
        <field name="arch" type="xml">
            <button name="action_create_invoice" position="after">
                <button name="create_package_invoice" type="object" string="Create Package Bill/Invoice" attrs="{'invisible': ['|',('package_id', '=', False), ('package_invoice_id', '!=', False)]}" groups="account.group_account_invoice"/>
                <field name="package_invoice_id" invisible="1"/>
            </button>
            <div name="button_box" position="inside">
                <button name="action_claim_view" type="object" class="oe_stat_button" icon="fa-pencil-square-o" groups="acs_hms_insurance.group_hms_insurance_officer">
                    <field string="Claims" name="claim_count" widget="statinfo"/>
                </button>
                <button name="action_patient_doc_view" string="Patient Doc" type="object" class="oe_stat_button" icon="fa-pencil-square-o"/>
            </div>
            <xpath expr="//field[@name='relative_number']" position="after">
                <field name="cashless"/>
                <field name="package_id"/>
            </xpath>
        </field>
    </record>

</odoo>