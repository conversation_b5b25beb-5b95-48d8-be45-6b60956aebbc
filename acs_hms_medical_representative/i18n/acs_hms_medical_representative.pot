# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* acs_hms_medical_representative
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-03-14 06:05+0000\n"
"PO-Revision-Date: 2022-03-14 06:05+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__meeting_count
msgid "# Meetings"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_ambulance_drive_count
msgid "#Ambulance Drives"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_blood_requisition_count
msgid "#Blood Donations"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_blood_issuance_count
msgid "#Blood Requisitions"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__announcement_count
msgid "#SMS Announcement Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__sms_count
msgid "#SMS Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__whatsapp_announcement_count
msgid "#whatsapp Announcement Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__whatsapp_count
msgid "#whatsapp Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Code:</b>"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Mobile:</b>"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Sex:</b>"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_payable_id
msgid "Account Payable"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_receivable_id
msgid "Account Receivable"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_amount_due
msgid "Acs Amount Due"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_needaction
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_needaction
msgid "Action Needed"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__active
msgid "Active"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__active_lang_count
msgid "Active Lang Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_ids
msgid "Activities"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_state
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_state
msgid "Activity State"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_type_icon
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__additional_info
msgid "Additional info"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Address"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__type
msgid "Address Type"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__age
msgid "Age"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__lang
msgid ""
"All the emails and documents sent to this contact will be translated in this"
" language."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_ambulance_drive_ids
msgid "Ambulance Drives"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Appointment"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Approve"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__approved
msgid "Approved"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_attachment_count
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__avatar_1920
msgid "Avatar"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__avatar_1024
msgid "Avatar 1024"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__avatar_128
msgid "Avatar 128"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__avatar_256
msgid "Avatar 256"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__avatar_512
msgid "Avatar 512"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__bank_account_count
msgid "Bank"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__bank_ids
msgid "Banks"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__barcode
msgid "Barcode"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_blacklisted
msgid "Blacklist"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_blood_issuance_ids
msgid "Blood Donations"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__blood_group
msgid "Blood Group"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_blood_requisition_ids
msgid "Blood Requisitions"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_bounce
msgid "Bounce"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__branch_id
msgid "Branch"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_ids
msgid "Business Commission"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__can_publish
msgid "Can Publish"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Cancel"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__cancelled
msgid "Cancelled"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__channel_ids
msgid "Channels"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_patient
msgid "Check if customer is linked with patient."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__employee
msgid "Check this box if this contact is an Employee."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__city
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "City"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.actions.act_window,help:acs_hms_medical_representative.action_medical_representative
msgid "Click to add a Medical Represenatative."
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.actions.act_window,help:acs_hms_medical_representative.action_medical_representative_visit
msgid "Click to add a Medical Representative Visit."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__color
msgid "Color Index"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commercial_partner_id
msgid "Commercial Entity"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_rule_ids
msgid "Commission Rules"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ref_company_ids
msgid "Companies that refers to partner"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_id
msgid "Company"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_name
msgid "Company Name"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commercial_company_name
msgid "Company Name Entity"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_type
msgid "Company Type"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_gid
msgid "Company database ID"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contact_address
msgid "Complete Address"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__child_ids
msgid "Contact"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contact_address_complete
msgid "Contact Address Complete"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__country_id
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Country"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__country_code
msgid "Country Code"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__create_uid
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__create_uid
msgid "Created by"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__create_date
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__create_date
msgid "Created on"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__credit_limit
msgid "Credit Limit"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__currency_id
msgid "Currency"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_stock_customer
msgid "Customer Location"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_payment_term_id
msgid "Customer Payment Terms"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__customer_rank
msgid "Customer Rank"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__date_visit
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__date
msgid "Date"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__birthday
msgid "Date of Birth"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__date_of_death
msgid "Date of Death"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__trust
msgid "Degree of trust you have in this debtor"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Description"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__display_name
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__display_name
msgid "Display Name"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__physician_id
msgid "Doctor"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__done
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Done"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__remark
msgid "Dr Remark"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__draft
msgid "Draft"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__partner_share
msgid ""
"Either customer (not a user), either shared user. Indicated the current "
"partner is a customer without access or with a limited access created for "
"sharing data."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email
msgid "Email"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__employee
msgid "Employee"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__employee_ids
msgid "Employees"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__employees_count
msgid "Employees Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_position_id
msgid "Fiscal Position"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_follower_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_follower_ids
msgid "Followers"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_partner_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_type_icon
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email_formatted
msgid "Formatted Email"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__gender
msgid "Gender"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "General Information"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__generated_otp_sms
msgid "Generated OTP SMS"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__generated_otp_whatsapp
msgid "Generated OTP Whatsapp"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_latitude
msgid "Geo Latitude"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_longitude
msgid "Geo Longitude"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__provide_commission
msgid "Give Commission"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__has_message
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__has_message
msgid "Has Message"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__hospital_name
msgid "Hospital Name"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__id
msgid "ID"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__im_status
msgid "IM Status"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "IMG"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_icon
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_icon
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__code
msgid "Identification Code"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__code
msgid "Identifier provided by the Health Center."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_needaction
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_unread
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_needaction
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_unread
msgid "If checked, new messages require your attention."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_sms_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_1920
msgid "Image"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_1024
msgid "Image 1024"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_128
msgid "Image 128"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_256
msgid "Image 256"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_512
msgid "Image 512"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__industry_id
msgid "Industry"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_warn
msgid "Invoice"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__type
msgid ""
"Invoice & Delivery addresses are used in sales orders. Private addresses are"
" only visible by authorized users."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_ids
msgid "Invoices"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_donor
msgid "Is Blood Donor"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_receiver
msgid "Is Blood Receiver"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_driver
msgid "Is Driver"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_is_follower
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_patient
msgid "Is Patient"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_published
msgid "Is Published"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_referring_doctor
msgid "Is Refereinng Physician"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_company
msgid "Is a Company"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__function
msgid "Job Position"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__journal_item_count
msgid "Journal Items"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__lang
msgid "Language"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit____last_update
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative____last_update
msgid "Last Modified on"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__write_uid
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__write_uid
msgid "Last Updated by"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__write_date
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__write_date
msgid "Last Updated on"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__calendar_last_notif_ack
msgid "Last notification marked as read from base Calendar"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.actions.act_window,name:acs_hms_medical_representative.action_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__medical_representative_id
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr_appointment
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr_main
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_tree
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "MR"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.actions.act_window,name:acs_hms_medical_representative.action_medical_representative_visit
msgid "MR Visit"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model,name:acs_hms_medical_representative.model_medical_representative
msgid "Medical Representative"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model,name:acs_hms_medical_representative.model_acs_mr_visit
msgid "Medical Visit"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_medium
msgid "Medium-sized image"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__meeting_ids
msgid "Meetings"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_error
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_warn_msg
msgid "Message for Invoice"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__picking_warn_msg
msgid "Message for Stock Picking"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_ids
msgid "Messages"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__mobile
msgid "Mobile"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Mr Registration"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__my_activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__name
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__name
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Name"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__physician_id
msgid "Name of the Doctor"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__medical_representative_id
msgid "Name of the Mr"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_calendar_event_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_summary
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_type_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__comment
msgid "Notes"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_needaction_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_error_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_needaction_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_error_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_unread_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_unread_counter
msgid "Number of unread messages"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ocn_token
msgid "OCN Token"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_otp_sms
msgid "OTP SMS"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_otp_whatsapp
msgid "OTP Whatsapp"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__parent_name
msgid "Parent name"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_id
msgid "Partner"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contract_ids
msgid "Partner Contracts"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__same_vat_partner_id
msgid "Partner with same Tax ID"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_patient_id
msgid "Patient"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__debit_limit
msgid "Payable Limit"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_payment_method_id
msgid "Payment Method"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__payment_token_count
msgid "Payment Token Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__payment_token_ids
msgid "Payment Tokens"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_percentage
msgid "Percentage"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone
msgid "Phone"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone_mobile_search
msgid "Phone/Mobile"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__plan_to_change_bike
msgid "Plan To Change Bike"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__plan_to_change_car
msgid "Plan To Change Car"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_payment_method_id
msgid ""
"Preferred payment method when paying this vendor. This is used to filter "
"vendor bills by preferred payment method to register payments in mass. Use "
"cases: create bank files for batch wires, check runs."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_product_pricelist
msgid "Pricelist"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__product_description
msgid "Product Description"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_name
msgid "Purpose"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ref
msgid "Reference"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__parent_id
msgid "Related Company"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__employee_ids
msgid "Related employees based on their private address"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Remark"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_user_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_id
msgid "Restrict publishing to this website."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_role_id
msgid "Role"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__sms_ids
msgid "SMS"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_sms_error
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__user_id
msgid "Salesperson"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone_sanitized
msgid "Sanitized Number"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__invoice_warn
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__self
msgid "Self"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_share
msgid "Share Partner"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_expiration
msgid "Signup Expiration"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_token
msgid "Signup Token"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_type
msgid "Signup Token Type"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_valid
msgid "Signup Token is Valid"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_url
msgid "Signup URL"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__state_id
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "State"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__state
msgid "Status"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_state
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__picking_warn
msgid "Stock Picking"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__street
msgid "Street"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Street 2..."
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Street..."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__street2
msgid "Street2"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__supplier_rank
msgid "Supplier Rank"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__category_id
msgid "Tags"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__vat
msgid "Tax ID"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_position_id
msgid ""
"The fiscal position determines the taxes/accounts used for this contact."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__user_id
msgid "The internal user in charge of this contact."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__verified_mobile_sms
msgid "The mobile number is verified using the SMS message"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__verified_mobile_whatsapp
msgid "The mobile number is verified using the Whatsapp message"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__tz
msgid "Timezone"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__tz_offset
msgid "Timezone offset"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__title
msgid "Title"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__total_invoiced
msgid "Total Invoiced"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__debit
msgid "Total Payable"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__credit
msgid "Total Receivable"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__credit
msgid "Total amount this customer owes you."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__debit
msgid "Total amount you have to pay to this vendor."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_decoration
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_unread
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_unread
msgid "Unread Messages"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_unread_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_unread_counter
msgid "Unread Messages Counter"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__barcode
msgid "Use a barcode to identify this contact."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__ocn_token
msgid "Used for sending notification to registered devices"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__user_ids
msgid "Users"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__currency_id
msgid "Utility field to express amount currency"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_stock_supplier
msgid "Vendor Location"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__verified_mobile_sms
msgid "Verified SMS"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__verified_mobile_whatsapp
msgid "Verified Whatsapp"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_published
msgid "Visible on current website"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_calendar
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_pivot
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_search
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_tree
msgid "Visit"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__visitor_ids
msgid "Visitors"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__acs_webcam_url
msgid "Webcam Field"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_id
msgid "Website"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website
msgid "Website Link"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__website_message_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_url
msgid "Website URL"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__website_message_ids
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "ZIP"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__zip
msgid "Zip"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__whatsapp_ids
msgid "whatsapp"
msgstr ""
