# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* acs_hms_medical_representative
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-11-02 10:23+0000\n"
"PO-Revision-Date: 2019-11-02 10:23+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Code:</b>"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Mobile:</b>"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Sex:</b>"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_payable_id
msgid "Account Payable"
msgstr "حساب الدائنون"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_receivable_id
msgid "Account Receivable"
msgstr "حساب المدينون"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_needaction
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__active
msgid "Active"
msgstr "نشط"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__active_lang_count
msgid "Active Lang Count"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_ids
msgid "Activities"
msgstr "الأنشطة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_state
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__additional_info
msgid "Additional info"
msgstr "معلومات إضافية"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Address"
msgstr "العنوان"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__type
msgid "Address Type"
msgstr "نوع العنوان"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__age
msgid "Age"
msgstr "العمر"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__lang
msgid ""
"All the emails and documents sent to this contact will be translated in this"
" language."
msgstr ""
"سيتم ترجمة كافة رسائل البريد الإلكتروني والمستندات المرسلة لجهة الاتصال "
"المختارة إلى هذه اللغة."

#. module: acs_hms_medical_representative
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Appointment"
msgstr "الموعد"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Approve"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__approved
msgid "Approved"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_attachment_count
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__bank_account_count
msgid "Bank"
msgstr "البنك"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__bank_ids
msgid "Banks"
msgstr "البنوك"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_blacklisted
msgid "Blacklist"
msgstr "القائمة السوداء"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__blood_group
msgid "Blood Group"
msgstr "فصيلة الدم"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_bounce
msgid "Bounce"
msgstr "المرتد"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_ids
msgid "Business Commission"
msgstr "عمولة الأعمال"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__can_publish
msgid "Can Publish"
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Cancel"
msgstr "إلغاء"

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__cancelled
msgid "Cancelled"
msgstr "ملغي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__channel_ids
msgid "Channels"
msgstr "القنوات"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr "فحص ما اذا كانت جهة الاتصال مؤسسة، وإلا كانت شخصًا"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__employee
msgid "Check this box if this contact is an Employee."
msgstr "قم بتحديد هذا المربع إذا كانت جهة الاتصال المختارة موظفًا."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__city
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "City"
msgstr "المدينة"

#. module: acs_hms_medical_representative
#: model_terms:ir.actions.act_window,help:acs_hms_medical_representative.action_medical_representative
msgid "Click to add a Medical Represenatative."
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.actions.act_window,help:acs_hms_medical_representative.action_medical_representative_visit
msgid "Click to add a Medical Representative Visit."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__color
msgid "Color Index"
msgstr "معرف اللون"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commercial_partner_id
msgid "Commercial Entity"
msgstr "الكيان التجاري"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_percentage
msgid "Commission Percentage"
msgstr "نسبة العموله"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ref_company_ids
msgid "Companies that refers to partner"
msgstr "مؤسسات تشير إلي شركاء"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_id
msgid "Company"
msgstr "الشركة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_name
msgid "Company Name"
msgstr "اسم المؤسسة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commercial_company_name
msgid "Company Name Entity"
msgstr "كيان اسم المؤسسة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_type
msgid "Company Type"
msgstr "نوع المؤسسة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_gid
msgid "Company database ID"
msgstr "الرقم التعريفي لقاعدة البيانات"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contact_address
msgid "Complete Address"
msgstr "العنوان الكامل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__child_ids
msgid "Contact"
msgstr "جهة الاتصال"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contact_address_complete
msgid "Contact Address Complete"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__payment_token_count
msgid "Count Payment Token"
msgstr "عدد رموز السداد السرية"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "عدد الرسائل المرتدة لجهة الاتصال هذه"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__country_id
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Country"
msgstr "الدولة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__create_uid
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__create_date
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__credit_limit
msgid "Credit Limit"
msgstr "حد الائتمان"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__currency_id
msgid "Currency"
msgstr "العملة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_stock_customer
msgid "Customer Location"
msgstr "مكان العميل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_payment_term_id
msgid "Customer Payment Terms"
msgstr "شروط السداد للعميل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__customer_rank
msgid "Customer Rank"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__date_visit
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__date
msgid "Date"
msgstr "التاريخ"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__birthday
msgid "Date of Birth"
msgstr "تاريخ الميلاد"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__date_of_death
msgid "Date of Death"
msgstr "تاريخ الوفاة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__trust
msgid "Degree of trust you have in this debtor"
msgstr "درجة ثقتكم في هذا المدين"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Description"
msgstr "الوصف"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__display_name
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__physician_id
msgid "Doctor"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__done
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Done"
msgstr "المنتهية"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__remark
msgid "Dr Remark"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__draft
msgid "Draft"
msgstr "مسودة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__partner_share
msgid ""
"Either customer (not a user), either shared user. Indicated the current "
"partner is a customer without access or with a limited access created for "
"sharing data."
msgstr ""
"إما عميل (وليس مستخدم) ، إما مستخدم مشترك. يشار إلى أن الشريك الحالي هو عميل"
" بدون وصول أو مع وصول محدود تم إنشاؤه لمشاركة البيانات."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__employee
msgid "Employee"
msgstr "الموظف"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_position_id
msgid "Fiscal Position"
msgstr "الموقف الضريبي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_follower_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_follower_ids
msgid "Followers"
msgstr "المتابعون"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_channel_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_channel_ids
msgid "Followers (Channels)"
msgstr "المتابعون (القنوات)"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_partner_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعون (الشركاء)"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "تنسيق البريد الإلكتروني \"الاسم <email@domain>\""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email_formatted
msgid "Formatted Email"
msgstr "البريد الإلكتروني المنسق"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__gender
msgid "Gender"
msgstr "الجنس"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "General Information"
msgstr "المعلومات العامة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_latitude
msgid "Geo Latitude"
msgstr "خط العرض الجغرافي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_longitude
msgid "Geo Longitude"
msgstr "خط الطول الجغرافي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__provide_commission
msgid "Give Commission"
msgstr "سدد العمولة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "يحتوي قيود غير مسواة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__hospital_name
msgid "Hospital Name"
msgstr "اسم المستشفى"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__id
msgid "ID"
msgstr "المُعرف"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__im_status
msgid "IM Status"
msgstr "حالة المحادثات الفورية"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_icon
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_icon
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__code
msgid "Identification Code"
msgstr "رمز التعريف"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__code
msgid "Identifier provided by the Health Center."
msgstr "المعرف المقدم من قبل المركز الصحي."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_needaction
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_unread
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_needaction
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_unread
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة تحتاج لرؤيتها."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_sms_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_blacklisted
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__phone_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"إذا كان البريد الإلكتروني في القائمة السوداء، لن يستقبل صاحبه أي مراسلات "
"جماعية من أي قائمة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_1920
msgid "Image"
msgstr "صورة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_1024
msgid "Image 1024"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_128
msgid "Image 128"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_256
msgid "Image 256"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_512
msgid "Image 512"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__industry_id
msgid "Industry"
msgstr "مجال العمل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_warn
msgid "Invoice"
msgstr "فاتورة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__type
msgid ""
"Invoice & Delivery addresses are used in sales orders. Private addresses are"
" only visible by authorized users."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_ids
msgid "Invoices"
msgstr "الفواتير"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_is_follower
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_published
msgid "Is Published"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_referring_doctor
msgid "Is Refereinng Physician"
msgstr "الطبيب المحول"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_company
msgid "Is a Company"
msgstr "هذه مؤسسة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__function
msgid "Job Position"
msgstr "المنصب الوظيفي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__journal_item_count
msgid "Journal Items"
msgstr "عناصر اليومية"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__lang
msgid "Language"
msgstr "اللغة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit____last_update
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__write_uid
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__write_date
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"آخر مرة تم إجراء تسوية بين فواتير ومدفوعات هذا الشريك. يتم تحديد هذا الحقل "
"إذا كان هناك مبلغ مدين غير مسوى ومبلغ دائن غير مسوى أو إذا نقرت زر \"تم\"."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "تاريخ آخر مطابقة للفواتير والمدفوعات"

#. module: acs_hms_medical_representative
#: model:ir.actions.act_window,name:acs_hms_medical_representative.action_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__medical_representative_id
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr_appointment
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr_main
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_tree
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "MR"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.actions.act_window,name:acs_hms_medical_representative.action_medical_representative_visit
msgid "MR Visit"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_main_attachment_id
msgid "Main Attachment"
msgstr "المرفق الرئيسي"

#. module: acs_hms_medical_representative
#: model:ir.model,name:acs_hms_medical_representative.model_medical_representative
msgid "Medical Representative"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model,name:acs_hms_medical_representative.model_acs_mr_visit
msgid "Medical Visit"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_medium
msgid "Medium-sized image"
msgstr "صورة متوسطة الحجم"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_error
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_warn_msg
msgid "Message for Invoice"
msgstr "رسالة للفاتورة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "رسالة لاختيار الأسهم"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__mobile
msgid "Mobile"
msgstr "الهاتف المحمول"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Mr Registration"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__name
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Name"
msgstr "الاسم"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__physician_id
msgid "Name of the Doctor"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__medical_representative_id
msgid "Name of the Mr"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_summary
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_type_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email_normalized
msgid "Normalized Email"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__comment
msgid "Notes"
msgstr "الملاحظات"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_needaction_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_error_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الاخطاء"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_needaction_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "عدد الرسائل التي تتطلب إجراء"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_error_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_unread_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_unread_counter
msgid "Number of unread messages"
msgstr "عدد الرسائل الجديدة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__online_partner_bank_account
msgid "Online Partner Bank Account"
msgstr "الرمز الرسمي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__online_partner_vendor_name
msgid "Online Partner Vendor Name"
msgstr "اسم مورد الشريك عبر الإنترنت"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__parent_name
msgid "Parent name"
msgstr "اسم الأساس"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contract_ids
msgid "Partner Contracts"
msgstr "عقود الشريك"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__same_vat_partner_id
msgid "Partner with same Tax ID"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__debit_limit
msgid "Payable Limit"
msgstr "حدود الدين المستحق"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__payment_token_ids
msgid "Payment Tokens"
msgstr "رموز السداد السرية"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone
msgid "Phone"
msgstr "رقم الهاتف"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone_blacklisted
msgid "Phone Blacklisted"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_product_pricelist
msgid "Pricelist"
msgstr "قائمه الأسعار"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__product_description
msgid "Product Description"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_name
msgid "Purpose"
msgstr "الغرض"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ref
msgid "Reference"
msgstr "رقم الإشارة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__parent_id
msgid "Related Company"
msgstr "مؤسسة متعلقة"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Remark"
msgstr "ملاحظه"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_user_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_id
msgid "Restrict publishing to this website."
msgstr "قصر إمكانية النشر على هذا الموقع."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_sms_error
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل القصيرة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__user_id
msgid "Salesperson"
msgstr "مسئول المبيعات"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone_sanitized
msgid "Sanitized Number"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__invoice_warn
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"اختيار الخيار \"تحذير\" سوف يخطر المستخدم مع الرسالة، واختيار \"حجب رسالة\" "
"سيظهر رسالة خطأ مع الرسالة ويوقف التدفق. يجب كتابة الرسالة في الحقل التالي."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__self
msgid "Self"
msgstr "نفس"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__name
msgid "Sequence"
msgstr "المسلسل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_share
msgid "Share Partner"
msgstr "مشاركة الشريك"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_expiration
msgid "Signup Expiration"
msgstr "انتهاء صلاحية التسجيل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_token
msgid "Signup Token"
msgstr "كلمة سر التسجيل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_type
msgid "Signup Token Type"
msgstr "نوع كلمة سر التسجيل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_valid
msgid "Signup Token is Valid"
msgstr "كلمة سر التسجيل صالحة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_url
msgid "Signup URL"
msgstr "رابط التسجيل"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__state_id
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "State"
msgstr "المحافظة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__state
msgid "Status"
msgstr "الحالة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_state
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الحالة على أساس الأنشطة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__picking_warn
msgid "Stock Picking"
msgstr "إلتقاط المخزون"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__street
msgid "Street"
msgstr "الشارع"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Street 2..."
msgstr "الشارع 2..."

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Street..."
msgstr "الشارع..."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__street2
msgid "Street2"
msgstr "الشارع 2"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__supplier_rank
msgid "Supplier Rank"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__category_id
msgid "Tags"
msgstr "الوسوم"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__vat
msgid "Tax ID"
msgstr "معرف الضريبة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__online_partner_bank_account
msgid ""
"Technical field used to store information from plaid/yodlee to match partner"
msgstr "حقل تقني يُستخدم لحفظ معلومات من plaid/yodlee لمطابقتها مع الشريك"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__online_partner_vendor_name
msgid ""
"Technical field used to store information from plaid/yodlee to match partner"
" (used when a purchase is made)"
msgstr ""
"حقل تقني يُستخدم لحفظ معلومات من plaid/yodlee لمطابقتها مع الشريك (يُستخدم "
"عند القيام بعملية شراء)"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""
"رقم التعريف الضريبي. قم بإكماله إذا كانت جهة الاتصال تخضع للضرائب الحكومية. "
"يُستخدم في بعض المستندات القانونية."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_position_id
msgid ""
"The fiscal position determines the taxes/accounts used for this contact."
msgstr ""
"يحدد الموقف الضريبي الضرائب/الحسابات المستخدمة لجهة الاتصال المُختارة."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_url
msgid "The full URL to access the document through the website."
msgstr "الرابط الكامل للوصول للمستند من خلال الموقع."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__user_id
msgid "The internal user in charge of this contact."
msgstr "المستخدم الداخلي مسئول عن هذا العقد."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"لدى الشريك حساب مدين ودائن واحد على الأقل لم يُسوَ منذ أخر تطابق بين "
"الفواتير والمدفوعات."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr "الموقع التخزيني المُستخدم كوجهة عند إرسال بضاعة لجهة الاتصال هذه."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr "الموقع التخزيني المُستخدم كمصدر عند إرسال بضاعة لجهة الاتصال هذه."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"سيتم استخدام هذا الحساب بدلًا من الحساب الافتراضي كحساب دائن للشريك الحالي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"سيتم استخدام هذا الحساب بدلًا من الحساب الافتراضي كحساب مدين للشريك الحالي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""
"سيُستخدم شرط السداد هذا بدلًا من الشرط الافتراضي لأوامر الشراء وفواتير "
"المورد"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""
"سيُستخدم شرط السداد هذا بدلًا من الشرط الافتراضي لإصدار أوامر المبيعات "
"وفواتير العملاء"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr "سوف تستخدم قائمة الأسعار هذه بدلاً من الافتراضية للبيع للشريك الحالي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__tz
msgid "Timezone"
msgstr "المنطقة الزمنية"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__tz_offset
msgid "Timezone offset"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__title
msgid "Title"
msgstr "العنوان"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__total_invoiced
msgid "Total Invoiced"
msgstr "إجمالي قيمة الفواتير"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__debit
msgid "Total Payable"
msgstr "الاجمالى الدائن"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__credit
msgid "Total Receivable"
msgstr "الإجمالي المدين"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__credit
msgid "Total amount this customer owes you."
msgstr "إجمالي المبلغ المُستحق من هذا العميل."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__debit
msgid "Total amount you have to pay to this vendor."
msgstr "إجمالي المبلغ الواجب سداده لهذا المورد."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_decoration
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_unread
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_unread
msgid "Unread Messages"
msgstr "الرسائل الجديدة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_unread_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_unread_counter
msgid "Unread Messages Counter"
msgstr "عدد الرسائل الجديدة"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__user_ids
msgid "Users"
msgstr "المستخدمون"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__currency_id
msgid "Utility field to express amount currency"
msgstr "حقل مساعد للتعبير عن عملة المبلغ"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_stock_supplier
msgid "Vendor Location"
msgstr "مكان المورد"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "شروط السداد كمورد"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_published
msgid "Visible on current website"
msgstr "ظاهرة في الموقع الحالي"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_calendar
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_pivot
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_search
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_tree
msgid "Visit"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__visitor_ids
msgid "Visitors"
msgstr ""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_id
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website
msgid "Website Link"
msgstr "رابط الموقع"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__website_message_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_url
msgid "Website URL"
msgstr "رابط الموقع"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__website_message_ids
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__tz
msgid ""
"When printing documents and exporting/importing data, time values are computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your web client."
msgstr ""

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "ZIP"
msgstr "الرمز البريدي"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__zip
msgid "Zip"
msgstr "الرمز البريدي"
