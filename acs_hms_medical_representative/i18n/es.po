# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* acs_hms_medical_representative
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 13.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-11-02 10:23+0000\n"
"PO-Revision-Date: 2020-06-26 02:06-0500\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.3.1\n"
"Last-Translator: \n"
"Language: es\n"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Code:</b>"
msgstr "<b>Código:</b>"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Mobile:</b>"
msgstr "<b>Móvil:</b>"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Sex:</b>"
msgstr "<b>Sexo:</b>"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_payable_id
msgid "Account Payable"
msgstr "Cuenta por Pagar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_receivable_id
msgid "Account Receivable"
msgstr "Cuenta por Cobrar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_needaction
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__active
msgid "Active"
msgstr "Activo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__active_lang_count
msgid "Active Lang Count"
msgstr "Lenguaje activo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_decoration
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de excepción de actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_state
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_state
msgid "Activity State"
msgstr "Estado de actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__additional_info
msgid "Additional info"
msgstr "Información adicional"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Address"
msgstr "Dirección"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__type
msgid "Address Type"
msgstr "Tipo de direcciónes"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__age
msgid "Age"
msgstr "Edad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__lang
msgid ""
"All the emails and documents sent to this contact will be translated in this "
"language."
msgstr ""
"Todos los correos electrónicos y documentos enviados a este contacto serán "
"traducidos a este idioma."

#. module: acs_hms_medical_representative
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Appointment"
msgstr "Cita"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Approve"
msgstr "Aprobar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__approved
msgid "Approved"
msgstr "Aprobado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_attachment_count
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_attachment_count
msgid "Attachment Count"
msgstr "Recuento de adjuntos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__bank_account_count
msgid "Bank"
msgstr "Banco"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__bank_ids
msgid "Banks"
msgstr "Bancos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_blacklisted
msgid "Blacklist"
msgstr "Lista negra"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__blood_group
msgid "Blood Group"
msgstr "Grupo sanguíneo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_bounce
msgid "Bounce"
msgstr "Rebote"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_ids
msgid "Business Commission"
msgstr "Comisión de la empresa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Cancel"
msgstr "Anular"

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__cancelled
msgid "Cancelled"
msgstr "Anulado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__channel_ids
msgid "Channels"
msgstr "Canales"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr ""
"Marque esta casilla si el contacto es una empresa. En caso contrario, es una "
"persona"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__employee
msgid "Check this box if this contact is an Employee."
msgstr "Marque esta casilla si este contacto es un Empleado."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__city
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "City"
msgstr "Ciudad"

#. module: acs_hms_medical_representative
#: model_terms:ir.actions.act_window,help:acs_hms_medical_representative.action_medical_representative
msgid "Click to add a Medical Represenatative."
msgstr "Haga clic para agregar un visitador médico."

#. module: acs_hms_medical_representative
#: model_terms:ir.actions.act_window,help:acs_hms_medical_representative.action_medical_representative_visit
msgid "Click to add a Medical Representative Visit."
msgstr "Haga clic para agregar una visita de representante médico."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__color
msgid "Color Index"
msgstr "Índice de color"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entidad comercial"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_percentage
msgid "Commission Percentage"
msgstr "Porcentaje de la Comisión"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ref_company_ids
msgid "Companies that refers to partner"
msgstr "Compañías que se refieren al cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_id
msgid "Company"
msgstr "Empresa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_name
msgid "Company Name"
msgstr "Nombre de la empresa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commercial_company_name
msgid "Company Name Entity"
msgstr "Entidad de nombre de la empresa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_type
msgid "Company Type"
msgstr "Tipo de empresa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_gid
msgid "Company database ID"
msgstr "ID de base de datos de empresa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contact_address
msgid "Complete Address"
msgstr "Dirección completa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__child_ids
msgid "Contact"
msgstr "Contacto"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contact_address_complete
msgid "Contact Address Complete"
msgstr "Dirección de contacto completa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__payment_token_count
msgid "Count Payment Token"
msgstr "Cuenta Token de Pago"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr ""
"Contador del número de correos electrónicos rebotados para este contacto"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__country_id
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Country"
msgstr "País"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__create_uid
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__create_date
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__create_date
msgid "Created on"
msgstr "Creada el"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__credit_limit
msgid "Credit Limit"
msgstr "Límite de crédito"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_stock_customer
msgid "Customer Location"
msgstr "Ubicación del cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Terminos de pago del cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__customer_rank
msgid "Customer Rank"
msgstr "Rango del cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__date_visit
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__date
msgid "Date"
msgstr "Fecha"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__birthday
msgid "Date of Birth"
msgstr "Fecha de nacimiento"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__date_of_death
msgid "Date of Death"
msgstr "Fecha de muerte"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__trust
msgid "Degree of trust you have in this debtor"
msgstr "Grado de confianza que tiene en este deudor"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Description"
msgstr "Descripción"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__display_name
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__physician_id
msgid "Doctor"
msgstr "Doctor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__done
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Done"
msgstr "Hecho"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__remark
msgid "Dr Remark"
msgstr "Observaciones del Dr."

#. module: acs_hms_medical_representative
#: model:ir.model.fields.selection,name:acs_hms_medical_representative.selection__acs_mr_visit__state__draft
msgid "Draft"
msgstr "Borrador"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__partner_share
msgid ""
"Either customer (not a user), either shared user. Indicated the current "
"partner is a customer without access or with a limited access created for "
"sharing data."
msgstr ""
"Cualquier cliente (no un usuario), cualquier usuario compartido. Indica que "
"el socio actual es un cliente sin acceso o con un acceso limitado creado "
"para compartir datos."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email
msgid "Email"
msgstr "Email"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__employee
msgid "Employee"
msgstr "Empleado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"Campo utilizado para almacenar el número de teléfono. Ayuda a acelerar las "
"búsquedas y comparaciones."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_position_id
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_follower_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_channel_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_channel_ids
msgid "Followers (Channels)"
msgstr "Seguidores (Canales)"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_partner_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "Formato dirección email \"Nombre <email@dominio>\""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email_formatted
msgid "Formatted Email"
msgstr "Email formateado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__gender
msgid "Gender"
msgstr "Género"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "General Information"
msgstr "Información general"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_latitude
msgid "Geo Latitude"
msgstr "Latitude geográfica"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_longitude
msgid "Geo Longitude"
msgstr "Longitud geográfica"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__provide_commission
msgid "Give Commission"
msgstr "Dar comisión"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "Tiene asientos sin reconciliar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__hospital_name
msgid "Hospital Name"
msgstr "Nombre del hospital"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__id
msgid "ID"
msgstr "ID"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__im_status
msgid "IM Status"
msgstr "Estado del chat"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_icon
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_icon
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar actividad de excepción."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__code
msgid "Identification Code"
msgstr "Código de identificación"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__code
msgid "Identifier provided by the Health Center."
msgstr "Identificador proporcionado por el Centro de Salud."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_needaction
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_unread
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_needaction
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_unread
msgid "If checked, new messages require your attention."
msgstr "Si está marcado, nuevos mensajes requerirán tu atención."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_sms_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tendrían errores de entrega."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_blacklisted
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__phone_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Si la dirección de correo electrónico está en la lista negra, el contacto ya "
"no recibirá correo masivo, de ninguna lista"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_1920
msgid "Image"
msgstr "Imagen"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_1024
msgid "Image 1024"
msgstr "Imagen 1024"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_512
msgid "Image 512"
msgstr "Imagen 512"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__industry_id
msgid "Industry"
msgstr "Industria"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_warn
msgid "Invoice"
msgstr "Factura"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__type
msgid ""
"Invoice & Delivery addresses are used in sales orders. Private addresses are "
"only visible by authorized users."
msgstr ""
"Las direcciones de factura y entrega se utilizan en pedidos de ventas. Las "
"direcciones privadas solo son visibles para los usuarios autorizados."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_ids
msgid "Invoices"
msgstr "Facturas"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_is_follower
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_is_follower
msgid "Is Follower"
msgstr "Es seguidor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_published
msgid "Is Published"
msgstr "Esta Publicado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_referring_doctor
msgid "Is Refereinng Physician"
msgstr "Es médico de referencia"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_company
msgid "Is a Company"
msgstr "Es una empresa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__function
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__journal_item_count
msgid "Journal Items"
msgstr "Diarios contables"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__lang
msgid "Language"
msgstr "Idioma"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit____last_update
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative____last_update
msgid "Last Modified on"
msgstr "Última modificación"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__write_uid
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__write_date
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__write_date
msgid "Last Updated on"
msgstr "Última actualización"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__last_time_entries_checked
msgid ""
"Last time the invoices & payments matching was performed for this partner. "
"It is set either if there's not at least an unreconciled debit and an "
"unreconciled credit or if you click the \"Done\" button."
msgstr ""
"Última vez que se realizó la reconciliación de facturas y pagos para este "
"contacto. Se establece si hay al menos un débito y un crédito no "
"reconciliados o si hace clic en el botón \"Listo\"."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Fecha de la última reconciliación de facturas y pagos"

#. module: acs_hms_medical_representative
#: model:ir.actions.act_window,name:acs_hms_medical_representative.action_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__medical_representative_id
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr_appointment
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr_main
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_tree
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "MR"
msgstr "VM"

#. module: acs_hms_medical_representative
#: model:ir.actions.act_window,name:acs_hms_medical_representative.action_medical_representative_visit
msgid "MR Visit"
msgstr "Visita de VM"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjunto principal"

#. module: acs_hms_medical_representative
#: model:ir.model,name:acs_hms_medical_representative.model_medical_representative
msgid "Medical Representative"
msgstr "Representante Médico (VM)"

#. module: acs_hms_medical_representative
#: model:ir.model,name:acs_hms_medical_representative.model_acs_mr_visit
msgid "Medical Visit"
msgstr "Visita Médica"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_medium
msgid "Medium-sized image"
msgstr "Imagen de tamaño mediano"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_error
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_error
msgid "Message Delivery error"
msgstr "Error de entrega de mensajes"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_warn_msg
msgid "Message for Invoice"
msgstr "Mensaje para factura"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Mensaje para recolección de stock"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__mobile
msgid "Mobile"
msgstr "Móvil"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Mr Registration"
msgstr "Registro de VM"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__name
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Name"
msgstr "Nombre"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__physician_id
msgid "Name of the Doctor"
msgstr "Nombre del doctor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__medical_representative_id
msgid "Name of the Mr"
msgstr "Nombre del VM"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_summary
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_type_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email_normalized
msgid "Normalized Email"
msgstr "Email normalizado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__comment
msgid "Notes"
msgstr "Notas"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_needaction_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de Acciones"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_error_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_error_counter
msgid "Number of errors"
msgstr "Cantidad de errores"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_needaction_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Cantidad de mensajes que requieren una acción"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_error_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Cantidad de mensajes con error de entrega"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_unread_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_unread_counter
msgid "Number of unread messages"
msgstr "Cantidad de mensajes no leídos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__online_partner_bank_account
msgid "Online Partner Bank Account"
msgstr "Cuenta bancaria en línea de contacto"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__online_partner_vendor_name
msgid "Online Partner Vendor Name"
msgstr "Nombre del proveedor del contacto en línea"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__parent_name
msgid "Parent name"
msgstr "Nombre del padre"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_id
msgid "Partner"
msgstr "Cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contract_ids
msgid "Partner Contracts"
msgstr "Contratos del cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__same_vat_partner_id
msgid "Partner with same Tax ID"
msgstr "Cliente con el mismo ID de impuestos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__debit_limit
msgid "Payable Limit"
msgstr "Límite a pagar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__payment_token_ids
msgid "Payment Tokens"
msgstr "Tableros de pago"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone
msgid "Phone"
msgstr "Teléfono"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone_blacklisted
msgid "Phone Blacklisted"
msgstr "Teléfono en la lista negra"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_product_pricelist
msgid "Pricelist"
msgstr "Lista de precios"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__product_description
msgid "Product Description"
msgstr "Descripción del producto"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_name
msgid "Purpose"
msgstr "Propósito"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ref
msgid "Reference"
msgstr "Referencia"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__parent_id
msgid "Related Company"
msgstr "Empresa relacionada"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Remark"
msgstr "Observación"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_user_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_user_id
msgid "Responsible User"
msgstr "Usuario Responsable"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_id
msgid "Restrict publishing to this website."
msgstr "Restringir la publicación a este sitio web."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_sms_error
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega de SMS"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__user_id
msgid "Salesperson"
msgstr "Vendedor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone_sanitized
msgid "Sanitized Number"
msgstr "Número de sanidad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__invoice_warn
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"Seleccionar la opción de “ADVERTENCIA” notificará al usuario con el mensaje, "
"seleccionar “Bloqueo de mensaje” se produce una excepción con el mensaje y "
"bloquear el flujo. El mensaje tiene que ser escrita en el campo."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__self
msgid "Self"
msgstr "El mismo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__name
msgid "Sequence"
msgstr "Secuencia"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_share
msgid "Share Partner"
msgstr "Contacto Compartido"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_expiration
msgid "Signup Expiration"
msgstr "Expiración del ingreso"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_token
msgid "Signup Token"
msgstr "Token de ingreso"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_type
msgid "Signup Token Type"
msgstr "Tipo de Token de ingreso"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_valid
msgid "Signup Token is Valid"
msgstr "El token de registro es válido"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_url
msgid "Signup URL"
msgstr "URL de ingreso"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__state_id
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "State"
msgstr "Estado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__state
msgid "Status"
msgstr "Estado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_state
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Atrasos: La fecha de vencimiento ya ha pasado\n"
"Hoy: La fecha de la actividad es hoy\n"
"Planificado: Actividades futuras."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__picking_warn
msgid "Stock Picking"
msgstr "Recogida de Stock"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__street
msgid "Street"
msgstr "Calle"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Street 2..."
msgstr "Calle 2..."

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Street..."
msgstr "Calle…"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__street2
msgid "Street2"
msgstr "Calle2"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__supplier_rank
msgid "Supplier Rank"
msgstr "Rango del proveedor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__category_id
msgid "Tags"
msgstr "Etiquetas"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__vat
msgid "Tax ID"
msgstr "Céd/Pas/RUC"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__online_partner_bank_account
msgid ""
"Technical field used to store information from plaid/yodlee to match partner"
msgstr ""
"Campo técnico utilizado para almacenar información de plaid/yodlee para "
"emparejar socio"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__online_partner_vendor_name
msgid ""
"Technical field used to store information from plaid/yodlee to match partner "
"(used when a purchase is made)"
msgstr ""
"Campo técnico utilizado para almacenar información de plaid/yodlee para "
"emparejar socio (utilizada cuando se realiza una compra)"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__vat
msgid ""
"The Tax Identification Number. Complete it if the contact is subjected to "
"government taxes. Used in some legal statements."
msgstr ""
"El número de identificación fiscal. Rellenarlo si el contacto está sujeto a "
"impuestos estatal. Se utiliza en algunas declaraciones legales."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_position_id
msgid ""
"The fiscal position determines the taxes/accounts used for this contact."
msgstr ""
"La posición fiscal determina los impuestos/cuentas utilizados para este "
"contacto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__user_id
msgid "The internal user in charge of this contact."
msgstr "El usuario interno a cargo de este contacto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__has_unreconciled_entries
msgid ""
"The partner has at least one unreconciled debit and credit since last time "
"the invoices & payments matching was performed."
msgstr ""
"El contacto tiene al menos un débito o crédito no reconciliado desde la "
"última vez que se realizó la reconciliación de facturas y pagos."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr ""
"La ubicación utilizada como destino al enviar medicamentos a este contacto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr ""
"La ubicación utilizada como fuente al recibir medicamentos de este contacto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_payable_id
msgid ""
"This account will be used instead of the default one as the payable account "
"for the current partner"
msgstr ""
"Esta cuenta será utilizada en lugar de la predeterminada como cuenta por "
"pagar para el contacto actual"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_receivable_id
msgid ""
"This account will be used instead of the default one as the receivable "
"account for the current partner"
msgstr ""
"Esta cuenta se utilizará en lugar de la predeterminada como cuenta por "
"cobrar para el contacto actual"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can "
"contain more than strictly an email address."
msgstr ""
"Este campo se utiliza para buscar en la dirección de correo electrónico, ya "
"que el campo de correo electrónico principal puede contener más que una "
"dirección de correo electrónico."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_supplier_payment_term_id
msgid ""
"This payment term will be used instead of the default one for purchase "
"orders and vendor bills"
msgstr ""
"Este plazo de pago se utilizará en lugar del predeterminado para pedidos de "
"compra y facturas de proveedores"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_payment_term_id
msgid ""
"This payment term will be used instead of the default one for sales orders "
"and customer invoices"
msgstr ""
"Este plazo de pago se utilizará en lugar del predeterminado para pedidos de "
"ventas y facturas de cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_product_pricelist
msgid ""
"This pricelist will be used, instead of the default one, for sales to the "
"current partner"
msgstr ""
"Esta lista de precios se utilizará en lugar de la lista por defecto, para "
"las ventas al contacto actual"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__tz_offset
msgid "Timezone offset"
msgstr "Compensación de zona horaria"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__title
msgid "Title"
msgstr "Título"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__total_invoiced
msgid "Total Invoiced"
msgstr "Total facturado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__debit
msgid "Total Payable"
msgstr "Total a Pagar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__credit
msgid "Total Receivable"
msgstr "Total a Cobrar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__credit
msgid "Total amount this customer owes you."
msgstr "Importe total que este cliente le debe."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__debit
msgid "Total amount you have to pay to this vendor."
msgstr "Importe total que tiene que pagar a este proveedor."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_exception_decoration
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_unread
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_unread
msgid "Unread Messages"
msgstr "Mensajes no leídos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_unread_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contador de mensajes no leídos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__user_ids
msgid "Users"
msgstr "Usuarios"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__currency_id
msgid "Utility field to express amount currency"
msgstr "Campo útil para expresar importe en divisa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_stock_supplier
msgid "Vendor Location"
msgstr "Ubicación de Proveedor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Condiciones de pago del proveedor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_calendar
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_pivot
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_search
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_tree
msgid "Visit"
msgstr "Visita"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__visitor_ids
msgid "Visitors"
msgstr "Visitantes"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_id
msgid "Website"
msgstr "Página web"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website
msgid "Website Link"
msgstr "Enlace al Sitio Web"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__website_message_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio Web"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_url
msgid "Website URL"
msgstr "URL del Sitio Web"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__website_message_ids
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones Web"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__tz
msgid ""
"When printing documents and exporting/importing data, time values are "
"computed according to this timezone.\n"
"If the timezone is not set, UTC (Coordinated Universal Time) is used.\n"
"Anywhere else, time values are computed according to the time offset of your "
"web client."
msgstr ""
"Al imprimir documentos y exportar/importar datos, los valores de tiempo se "
"calculan según esta zona horaria.\n"
"Si no se establece la zona horaria, se utiliza UTC (hora universal "
"coordinada).\n"
"En cualquier otro lugar, los valores de tiempo se calculan de acuerdo con el "
"desplazamiento de tiempo de su cliente web."

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "ZIP"
msgstr "C.P."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__zip
msgid "Zip"
msgstr "C.P."
