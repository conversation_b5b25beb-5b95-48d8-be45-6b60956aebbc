# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* acs_hms_medical_representative
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-02-04 22:25+0000\n"
"PO-Revision-Date: 2020-06-26 02:07-0500\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"Language: es\n"
"X-Generator: Poedit 2.3.1\n"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Code:</b>"
msgstr "<b>Código:</b>"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Mobile:</b>"
msgstr "<b>Móvil:</b>"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_represntative_kanban
msgid "<b>Sex:</b>"
msgstr "<b>Sexo:</b>"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_payable_id
msgid "Account Payable"
msgstr "Cuenta a pagar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_receivable_id
msgid "Account Receivable"
msgstr "Cuenta a cobrar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_needaction
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_needaction
msgid "Action Needed"
msgstr "Acción Necesaria"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__active
msgid "Active"
msgstr "Activo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_state
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__additional_info
msgid "Additional info"
msgstr "Información adicional"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Address"
msgstr "Dirección"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__type
msgid "Address Type"
msgstr "Tipo de dirección"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__age
msgid "Age"
msgstr "Edad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__lang
msgid "All the emails and documents sent to this contact will be translated in this language."
msgstr "Todos los correos electrónicos y documentos enviados a este contacto se traducirán en este idioma."

#. module: acs_hms_medical_representative
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Appointment"
msgstr "Cita"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Approve"
msgstr "Aprobar"

#. module: acs_hms_medical_representative
#: selection:acs.mr.visit,state:0
msgid "Approved"
msgstr "Aprobado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_attachment_count
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_attachment_count
msgid "Attachment Count"
msgstr "Conteo de archivos adjuntos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__bank_account_count
msgid "Bank"
msgstr "Banco"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__bank_ids
msgid "Banks"
msgstr "Bancos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__barcode
msgid "Barcode"
msgstr "Código de barras"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_blacklisted
msgid "Blacklist"
msgstr "Lista negra"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__blood_group
msgid "Blood Group"
msgstr "Grupo sanguíneo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_bounce
msgid "Bounce"
msgstr "Rebote"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_ids
msgid "Business Commission"
msgstr "Comisión de la empresa"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Cancel"
msgstr "Cancelar"

#. module: acs_hms_medical_representative
#: selection:acs.mr.visit,state:0
msgid "Cancelled"
msgstr "Cancelado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__channel_ids
msgid "Channels"
msgstr "Canales"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_company
msgid "Check if the contact is a company, otherwise it is a person"
msgstr "Marque esta casilla si el contacto es una compañía. En caso contrario, es una persona."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__customer
msgid "Check this box if this contact is a customer. It can be selected in sales orders."
msgstr "Marque esta casilla si este contacto es un cliente. Se puede seleccionar en pedidos de venta."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__supplier
msgid "Check this box if this contact is a vendor. It can be selected in purchase orders."
msgstr "Marque esta casilla si este contacto es un proveedor. Se puede seleccionar en órdenes de compra."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__employee
msgid "Check this box if this contact is an Employee."
msgstr "Marque si el contacto es un empleado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__city
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "City"
msgstr "Ciudad"

#. module: acs_hms_medical_representative
#: model_terms:ir.actions.act_window,help:acs_hms_medical_representative.action_medical_representative_visit
msgid "Click to add a Medical Represenatative Visit."
msgstr "Haga clic para agregar una visita médica representativa."

#. module: acs_hms_medical_representative
#: model_terms:ir.actions.act_window,help:acs_hms_medical_representative.action_medical_representative
msgid "Click to add a Medical Represenatative."
msgstr "Haga clic para agregar un representante médico."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__color
msgid "Color Index"
msgstr "Índice de Colores"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entidad comercial"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commission_percentage
msgid "Commission Percentage"
msgstr "Porcentaje de comisión"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ref_company_ids
msgid "Companies that refers to partner"
msgstr "Compañías que se refieren a la empresa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_id
msgid "Company"
msgstr "Compañía"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_name
msgid "Company Name"
msgstr "Nombre de la compañía"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__commercial_company_name
msgid "Company Name Entity"
msgstr "Entidad del nombre de la compañía"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__company_type
msgid "Company Type"
msgstr "Tipo de compañía"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_gid
msgid "Company database ID"
msgstr "Base de datos empresas"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contact_address
msgid "Complete Address"
msgstr "Dirección completa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__child_ids
msgid "Contacts"
msgstr "Contactos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contracts_count
msgid "Contracts Count"
msgstr "Recuento de contratos"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__payment_token_count
msgid "Count Payment Token"
msgstr "Cuenta Token de Pago"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Contador del número de correos electrónicos rebotados de este contacto"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__country_id
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Country"
msgstr "País"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__create_uid
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__create_date
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__create_date
msgid "Created on"
msgstr "Creado el"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__credit_limit
msgid "Credit Limit"
msgstr "Crédito límite"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_stock_customer
msgid "Customer Location"
msgstr "Ubicación de cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_payment_term_id
msgid "Customer Payment Terms"
msgstr "Plazo de pago de cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__date_visit
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__date
msgid "Date"
msgstr "Fecha"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__dob
msgid "Date of Birth"
msgstr "Fecha de nacimiento"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__trust
msgid "Degree of trust you have in this debtor"
msgstr "Grado de confianza para este deudor"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Description"
msgstr "Descripción"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__display_name
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__physician_id
msgid "Doctor"
msgstr "Doctor"

#. module: acs_hms_medical_representative
#: selection:acs.mr.visit,state:0
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Done"
msgstr "Listo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__remark
msgid "Dr Remark"
msgstr "Observaciones del Dr."

#. module: acs_hms_medical_representative
#: selection:acs.mr.visit,state:0
msgid "Draft"
msgstr "Borrador"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__partner_share
msgid "Either customer (not a user), either shared user. Indicated the current partner is a customer without access or with a limited access created for sharing data."
msgstr "Cualquiera de los clientes (no un usuario), o usuario compartido. Indicó que el socio actual es un cliente sin acceso o con un acceso limitado creado para compartir datos."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email
msgid "Email"
msgstr "Correo electrónico"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__employee
msgid "Employee"
msgstr "Empleado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_account_position_id
msgid "Fiscal Position"
msgstr "Posición fiscal"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_follower_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_channel_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_channel_ids
msgid "Followers (Channels)"
msgstr "Seguidores (Canales)"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_partner_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Socios)"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__email_formatted
msgid "Format email address \"Name <email@domain>\""
msgstr "Dirección de email con formato \"Nombre <email@domain>\""

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__email_formatted
msgid "Formatted Email"
msgstr "Email formateado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__sex
msgid "Gender"
msgstr "Género"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "General Information"
msgstr "Información General"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__provide_commission
msgid "Give Commission"
msgstr "Dar comisión"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__has_unreconciled_entries
msgid "Has Unreconciled Entries"
msgstr "Tiene entradas no conciliadas"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__hospital_name
msgid "Hospital Name"
msgstr "Nombre del Hospital"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__id
msgid "ID"
msgstr "ID"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__im_status
msgid "IM Status"
msgstr "Estado del chat"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__code
msgid "Identification Code"
msgstr "Código de Identificación"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__code
msgid "Identifier provided by the Health Center."
msgstr "Identificador del Paciente Proporcionado por el Centro Médico"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_unread
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_unread
msgid "If checked new messages require your attention."
msgstr "Si está marcado, hay nuevos mensajes que requieren su atención"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_needaction
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcado, hay nuevos mensajes que requieren su atención."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_error
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra marcado, algunos mensajes tienen error de envío."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__is_blacklisted
msgid "If the email address is on the blacklist, the contact won't receive mass mailing anymore, from any list"
msgstr "Si la dirección de email esta en la lista negra, el contacto ya no recibirá correo masivo de cualquier lista."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image
msgid "Image"
msgstr "Imagen"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__industry_id
msgid "Industry"
msgstr "Industria"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__ref
msgid "Internal Reference"
msgstr "Referencia interna"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_warn
msgid "Invoice"
msgstr "Factura"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_ids
msgid "Invoices"
msgstr "Facturas"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_is_follower
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_referring_doctor
msgid "Is Refereinng Physician"
msgstr "Es médico de referencia"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__is_company
msgid "Is a Company"
msgstr "Es una compañia"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__customer
msgid "Is a Customer"
msgstr "Es Cliente"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__supplier
#, fuzzy
msgid "Is a Vendor"
msgstr "Es proveedor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__function
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__journal_item_count
msgid "Journal Items"
msgstr "Apuntes contables"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__lang
msgid "Language"
msgstr "Idioma"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit____last_update
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative____last_update
msgid "Last Modified on"
msgstr "Última modificación en"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__write_uid
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__write_date
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__last_time_entries_checked
msgid "Last time the invoices & payments matching was performed for this partner. It is set either if there's not at least an unreconciled debit and an unreconciled credit or if you click the \"Done\" button."
msgstr "Última vez que se conciliaros facturas y pagos de este asociado. Se configura incluso si no hay ningún débito o crédito por conciliar, o si pulsa el botón \"Hecho\"."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__last_time_entries_checked
msgid "Latest Invoices & Payments Matching Date"
msgstr "Fecha de la última conciliación de facturas y pagos"

#. module: acs_hms_medical_representative
#: model:ir.actions.act_window,name:acs_hms_medical_representative.action_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__medical_representative_id
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr_appointment
#: model:ir.ui.menu,name:acs_hms_medical_representative.menu_mr_main
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_tree
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "MR"
msgstr "VM"

#. module: acs_hms_medical_representative
#: model:ir.actions.act_window,name:acs_hms_medical_representative.action_medical_representative_visit
msgid "MR Visit"
msgstr "Visita de VM"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_main_attachment_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_main_attachment_id
msgid "Main Attachment"
msgstr "Adjuntos principales"

#. module: acs_hms_medical_representative
#: model:ir.model,name:acs_hms_medical_representative.model_medical_representative
msgid "Medical Representative"
msgstr "Representante Médico (VM)"

#. module: acs_hms_medical_representative
#: model:ir.model,name:acs_hms_medical_representative.model_acs_mr_visit
msgid "Medical Visit"
msgstr "Visita Médica"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_medium
msgid "Medium-sized image"
msgstr "Imagen de tamaño mediano"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__image_medium
msgid "Medium-sized image of this contact. It is automatically resized as a 128x128px image, with aspect ratio preserved. Use this field in form views or some kanban views."
msgstr "Imagen mediana de este contacto. Se redimensiona automáticamente a 128x128px, con el ratio de aspecto preservado. Se usa este campo en las vistas formulario y en algunas vistas kanban."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_error
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__invoice_warn_msg
msgid "Message for Invoice"
msgstr "Mensaje para factura"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "Mensaje para recolección de stock"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__mobile
msgid "Mobile"
msgstr "Móvil"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Mr Registration"
msgstr "Registro de VM"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__name
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Name"
msgstr "Nombre"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__physician_id
msgid "Name of the Doctor"
msgstr "Nombre del doctor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__medical_representative_id
msgid "Name of the Mr"
msgstr "Nombre del VM"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_date_deadline
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de siguiente actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_summary
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_type_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__comment
msgid "Notes"
msgstr "Notas"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_needaction_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_has_error_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_has_error_counter
msgid "Number of error"
msgstr "Número de error"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_needaction_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Número de mensajes que requieren una acción"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_has_error_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__message_unread_counter
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__message_unread_counter
msgid "Number of unread messages"
msgstr "Número de mensajes no leidos"

#. module: acs_hms_medical_representative
#: selection:acs.mr.visit,activity_state:0
msgid "Overdue"
msgstr "Atrasado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__parent_name
msgid "Parent name"
msgstr "Nombre del padre"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_id
msgid "Partner"
msgstr "Socio"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__contract_ids
msgid "Partner Contracts"
msgstr "Contratos de socios"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__debit_limit
msgid "Payable Limit"
msgstr "Límite a pagar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__payment_token_ids
msgid "Payment Tokens"
msgstr "Tableros de pago"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__phone
msgid "Phone"
msgstr "Teléfono"

#. module: acs_hms_medical_representative
#: selection:acs.mr.visit,activity_state:0
msgid "Planned"
msgstr "Planificado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_product_pricelist
msgid "Pricelist"
msgstr "Tarifa"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__product_description
msgid "Product Description"
msgstr "Descripción del producto"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_name
msgid "Purpose"
msgstr "Propósito"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_id
msgid "Registration Website"
msgstr "Sitio web de registro"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__parent_id
msgid "Related Company"
msgstr "Empresa relacionada"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_form
msgid "Remark"
msgstr "Observación"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__activity_user_id
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__user_id
msgid "Salesperson"
msgstr "Vendedor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__invoice_warn
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__picking_warn
msgid "Selecting the \"Warning\" option will notify user with the message, Selecting \"Blocking Message\" will throw an exception with the message and block the flow. The Message has to be written in the next field."
msgstr "Si selecciona la opción \"Aviso\" se notificará a los usuarios con el mensaje, si selecciona \"Mensaje de bloqueo\" se lanzará una excepción con el mensaje y se bloqueará el flujo. El mensaje debe escribirse en el siguiente campo."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__self
msgid "Self"
msgstr "Sí mismo"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__name
msgid "Sequence"
msgstr "Secuencia"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__partner_share
msgid "Share Partner"
msgstr "Compartir socio"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_expiration
msgid "Signup Expiration"
msgstr "Expiración del ingreso"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_token
msgid "Signup Token"
msgstr "Palabra de ingreso"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_type
msgid "Signup Token Type"
msgstr "Tipo de la palabra de ingreso"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_valid
msgid "Signup Token is Valid"
msgstr "La palabra de ingreso es válida"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__signup_url
msgid "Signup URL"
msgstr "URL de ingreso"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__image_small
msgid "Small-sized image"
msgstr "Imagen de tamaño pequeño"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__image_small
msgid "Small-sized image of this contact. It is automatically resized as a 64x64px image, with aspect ratio preserved. Use this field anywhere a small image is required."
msgstr "Imagen de tamaño pequeño de este contacto. Se redimensiona automáticamente a 64x64 px, con el ratio de aspecto preservado. Use este campo donde se requiera una imagen pequeña."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__state_id
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "State"
msgstr "Estado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__state
msgid "Status"
msgstr "Estatus"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__activity_state
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: La fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__picking_warn
msgid "Stock Picking"
msgstr "Recolección de Stock"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__street
msgid "Street"
msgstr "Calle"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Street 2..."
msgstr "Calle 2..."

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "Street..."
msgstr "Calle..."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__street2
msgid "Street2"
msgstr "Calle2"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__category_id
msgid "Tags"
msgstr "Etiquetas"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__vat
msgid "Tax ID"
msgstr "Céd/Pas/RUC"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__vat
msgid "The Tax Identification Number. Complete it if the contact is subjected to government taxes. Used in some legal statements."
msgstr "El número de identificación fiscal. Complételo si el contacto está sujeto a los impuestos del gobierno. Utilizado en algunas declaraciones legales."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_position_id
msgid "The fiscal position determines the taxes/accounts used for this contact."
msgstr "La posición fiscal determina los impuestos / cuentas utilizados para este contacto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__user_id
msgid "The internal user in charge of this contact."
msgstr "El usuario interno a cargo de este contacto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__has_unreconciled_entries
msgid "The partner has at least one unreconciled debit and credit since last time the invoices & payments matching was performed."
msgstr "El asociado tiene al menos un débito o crédito no conciliado desde la última vez que se realizó la conciliación de facturas y pagos."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__tz
msgid "The partner's timezone, used to output proper date and time values inside printed reports. It is important to set a value for this field. You should use the same timezone that is otherwise used to pick and render date and time values: your computer's timezone."
msgstr "Zona horaria de la empresa, usada para mostrar la fecha y hora adecuadas en los informes. Es importante establecer un valor para este campo. Debería usar la misma zona horaria que se coge en cualquier caso para mostrar los valores de fecha y hora: la zona horaria de su ordenador."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_stock_customer
msgid "The stock location used as destination when sending goods to this contact."
msgstr "La ubicación  utilizada como destino al enviar mercancías a este contacto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_stock_supplier
msgid "The stock location used as source when receiving goods from this contact."
msgstr "La ubicación  utilizada como fuente al recibir mercancías de este contacto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_payable_id
msgid "This account will be used instead of the default one as the payable account for the current partner"
msgstr "Esta cuenta se utilizará en lugar de la cuenta por defecto como la cuenta pendiente de pago del asociado actual."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_account_receivable_id
msgid "This account will be used instead of the default one as the receivable account for the current partner"
msgstr "Esta cuenta se utilizará en lugar de la cuenta por defecto como la cuenta pendiente de cobro del asociado actual."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__image
msgid "This field holds the image used as avatar for this contact, limited to 1024x1024px"
msgstr "Este campo contiene la imagen utilizada como avatar para este contacto, limitada a 1024x1024px"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_supplier_payment_term_id
msgid "This payment term will be used instead of the default one for purchase orders and vendor bills"
msgstr "Se utilizará esta condición de pago, en lugar de la predeterminada, para los pedidos de compra y las facturas de proveedor."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_payment_term_id
msgid "This payment term will be used instead of the default one for sales orders and customer invoices"
msgstr "Este término de pago se utilizará en lugar del predeterminado para pedidos de cliente y facturas de clientes"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__property_product_pricelist
msgid "This pricelist will be used, instead of the default one, for sales to the current partner"
msgstr "Esta tarifa se utilizará, en lugar de la por defecto, para las ventas de la empresa actual."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__tz_offset
msgid "Timezone offset"
msgstr "Compensación de zona horaria"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__title
msgid "Title"
msgstr "Título"

#. module: acs_hms_medical_representative
#: selection:acs.mr.visit,activity_state:0
msgid "Today"
msgstr "Hoy"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__total_invoiced
msgid "Total Invoiced"
msgstr "Total facturado"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__debit
msgid "Total Payable"
msgstr "Total a pagar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__credit
msgid "Total Receivable"
msgstr "Total a cobrar"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__credit
msgid "Total amount this customer owes you."
msgstr "Cantidad total que le debe este cliente."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__debit
msgid "Total amount you have to pay to this vendor."
msgstr "Cantidad total a pagar a este proveedor."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_unread
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_unread
msgid "Unread Messages"
msgstr "Mensajes por leer"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__message_unread_counter
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Contador de mensajes sin leer"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__barcode
msgid "Use a barcode to identify this contact from the Point of Sale."
msgstr "Use un código de barras para identificar este contacto desde el punto de venta."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__type
msgid "Used by Sales and Purchase Apps to select the relevant address depending on the context."
msgstr "Utilizado por Ventas y Aplicaciones de compra para seleccionar la dirección relevante según el contexto."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__user_ids
msgid "Users"
msgstr "Usuarios"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__currency_id
msgid "Utility field to express amount currency"
msgstr "Campo útil para expresar importe de la moneda."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_stock_supplier
msgid "Vendor Location"
msgstr "Ubicación de proveedor"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__property_supplier_payment_term_id
msgid "Vendor Payment Terms"
msgstr "Términos de Pago"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_calendar
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_pivot
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_search
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.view_medical_representative_tree
msgid "Visit"
msgstr "Visita"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website
msgid "Website"
msgstr "Sitio web"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_acs_mr_visit__website_message_ids
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: acs_hms_medical_representative
#: model:ir.model.fields,help:acs_hms_medical_representative.field_acs_mr_visit__website_message_ids
#: model:ir.model.fields,help:acs_hms_medical_representative.field_medical_representative__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicaciones del sitio web"

#. module: acs_hms_medical_representative
#: model_terms:ir.ui.view,arch_db:acs_hms_medical_representative.medical_representative_form
msgid "ZIP"
msgstr "C.P."

#. module: acs_hms_medical_representative
#: model:ir.model.fields,field_description:acs_hms_medical_representative.field_medical_representative__zip
msgid "Zip"
msgstr "C.P."
