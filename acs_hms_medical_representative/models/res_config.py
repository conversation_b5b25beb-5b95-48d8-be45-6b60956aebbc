# -*- coding: utf-8 -*-
# Part of AlmightyCS See LICENSE file for full copyright and licensing details.

from odoo import api, fields, models


class ResCompany(models.Model):
    _inherit = "res.company"

    mr_usage_location = fields.Many2one('stock.location', string='Vendor Location for Sample Products')
    mr_stock_location = fields.Many2one('stock.location', string='Stock Location for Sample Products')


class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    mr_usage_location = fields.Many2one('stock.location', 
        related='company_id.mr_usage_location', domain=[('usage','=','supplier')],
        string='Vendor Location for Sample Products', readonly=False)
    mr_stock_location = fields.Many2one('stock.location', 
        related='company_id.mr_stock_location', domain=[('usage','=','internal')],
        string='Stock Location for Sample Products', readonly=False)