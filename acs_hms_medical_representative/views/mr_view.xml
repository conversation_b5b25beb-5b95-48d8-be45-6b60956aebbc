<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="medical_represntative_kanban" model="ir.ui.view">
        <field name="name">medical.representative.kanban</field>
        <field name="model">medical.representative</field>
        <field name="type">kanban</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="display_name"/>
                <field name="parent_id"/>
                <field name="code"/>
                <field name="image_128"/>
                <field name="gender"/>
                <field name="mobile"/>
                <field name="function"/>
                <field name="id"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_global_click o_res_partner_kanban">
                            <div class="o_kanban_tags_section oe_kanban_partner_categories"/>
                            <t t-set="placeholder" t-value="'/base/static/img/avatar_grey.png'"/>
                            <div class="o_kanban_image">
                                <img t-att-src="kanban_image('medical.representative', 'image_128', record.id.raw_value, placeholder)" alt="IMG"/>
                            </div>
                            <div class="oe_kanban_details">
                                <strong class="oe_partner_heading">
                                    <field name="display_name"/><t t-if="record.parent_id.raw_value"> [<field name="parent_id"/>]</t>
                                </strong>
                                <ul>
                                    <li t-if="record.gender.raw_value">
                                        <b>Sex:</b>
                                        <field name="gender"/>
                                    </li>
                                    <li t-if="record.mobile.raw_value">
                                        <b>Mobile:</b>
                                        <field name="mobile"/>
                                    </li>
                                    <li t-if="record.code.raw_value">
                                        <b>Code:</b>
                                        <field name="code"/>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="medical_representative_tree" model="ir.ui.view">
        <field name="name">medical.representative.tree</field>
        <field name="model">medical.representative</field>
        <field name="arch" type="xml">
            <tree string="Medical Representative">
                <field name="code"/>
                <field name="parent_id" string="Company Name"/>
                <field name="name"/>
                <field name="gender"/>
                <field name="mobile"/>
            </tree>
        </field>
    </record>

    <record id="medical_representative_form" model="ir.ui.view">
        <field name="name">medical.representative.form</field>
        <field name="model">medical.representative</field>
        <field name="arch" type="xml">
            <form string="Medical Representative">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_mr_visit" string="Appointment" type="object" class="oe_stat_button" icon="fa-pencil-square-o"/>
                    </div>
                    <field name="image_1920" widget="image" class="oe_avatar" options="{'preview_image': 'image_128'}"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" default_focus="1" placeholder="Name" required="True"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code" readonly="1"/>
                            <field name="gender"/>
                        </group>
                        <group>
                            <field name="parent_id" string="Company Name"/>
                        </group>
                    </group>
                    <notebook name="patient_note">
                        <page name="info" string="General Information">
                            <group>
                                <group>
                                    <label for="street" string="Address"/>
                                    <div class="o_address_format">
                                        <field name="street" placeholder="Street..." class="o_address_street"/>
                                        <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                        <field name="city" placeholder="City" class="o_address_city"/>
                                        <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True}" context="{'country_id': country_id, 'zip': zip}"/>
                                        <field name="zip" placeholder="ZIP" class="o_address_zip"/>
                                        <field name="country_id" placeholder="Country" class="o_address_country" options="{'no_open': True, 'no_create': True}"/>
                                    </div>
                                </group>
                                <group>
                                    <field name="phone" widget="phone"/>
                                    <field name="mobile" widget="phone" required="True"/>
                                    <field name="email"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="action_medical_representative" model="ir.actions.act_window">
        <field name="name">Medical Representative</field>
        <field name="res_model">medical.representative</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Click to add a Medical Represenatative.
            </p>
        </field>
    </record>

    <!--form view for Activity -->
    <record id="view_medical_representative_form" model="ir.ui.view">
        <field name="name">Medical Representative</field>
        <field name="model">acs.mr.visit</field>
        <field name="arch" type="xml">
            <form string="MR">
                <header>
                    <button name="action_approve" string="Approve" class="oe_highlight" type="object" states="draft"/>
                    <button name="action_done" string="Done" class="oe_highlight" type="object" states="approved"/>
                    <button name="action_cancel" string="Cancel" class="oe_highlight" type="object" states="draft,approved"/>
                    <field name="state" widget="statusbar"  statusbar_visible="draft,receive"
                        statusbar_colors='{"draft":"blue","cancel":"red"}'/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="activity_name"/>
                            <field name="medical_representative_id"/>
                            <field name="company_id" groups="base.group_multi_company" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="date_visit"/>
                            <field name="physician_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="sample_lines" string="Sample Products/Medicines" attrs="{'invisible': [('state', 'in', ['draft'])]}">
                            <field name="sample_line_ids" nolabel="1" colspan="4" context="{'default_physician_id': physician_id, 'default_medical_representative_id': medical_representative_id}">
                                <tree string="Line" editable="top">
                                    <field name="product_id" expand="1" required="1"/>
                                    <field name="product_uom" required="1" groups="uom.group_uom"/>
                                    <field name="product_uom_category_id" invisible="1"/>
                                    <field name="qty" required="1"/>
                                    <field name="tracking" invisible="1"/>
                                    <field name="lot_id" context="{'acs_product_id': product_id}" options="{'no_create': True}" attrs="{'readonly': [('tracking','=','none')], 'required': [('tracking','!=','none')]}"/>
                                    <field name="price_unit" readonly="1"/>
                                    <field name="subtotal" readonly="1" sum="Total" optional="show"/>
                                    <field name="date" required="1"/>
                                    <field name="move_id" invisible="1"/>
                                    <field name="physician_id" optional="hide"/>
                                    <field name="medical_representative_id" optional="hide"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                    <field name="product_description" nolabel="1" placeholder="Description"/>
                    <field name="remark" nolabel="1" placeholder="Remark"/>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_medical_representative_tree" model="ir.ui.view">
        <field name="name">acs.mr.visit.tree</field>
        <field name="model">acs.mr.visit</field>
        <field name="arch" type="xml">
            <tree string="Visit">
                <field name="name"/>
                <field name="activity_name"/>
                <field name="date_visit"/>
                <field name="medical_representative_id"/>
                <field name="physician_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="view_medical_representative_search" model="ir.ui.view">
        <field name="name">acs.mr.visit.search</field>
        <field name="model">acs.mr.visit</field>
        <field name="arch" type="xml">
            <search string="Visit">
                <field name="name"/>
                <field name="date_visit"/>
                <field name="medical_representative_id"/>
                <field name="physician_id"/>
                <field name="state"/>
                <newline/>
            </search>
        </field>
    </record>

    <record id="view_medical_representative_calendar" model="ir.ui.view">
        <field name="name">acs.mr.visit.calendar</field>
        <field name="model">acs.mr.visit</field>
        <field name="arch" type="xml">
            <calendar string="Visit" color="medical_representative_id" date_start="date_visit">
                <field name="medical_representative_id"/>
                <field name="state"/>
            </calendar>
        </field>
    </record>

    <record id="view_medical_representative_pivot" model="ir.ui.view">
        <field name="name">acs.mr.visit.pivot</field>
        <field name="model">acs.mr.visit</field>
        <field name="arch" type="xml">
            <pivot string="Visit">
                <field name="date_visit" type="row"/>
                <field name="physician_id" type="row"/>
                <field name="medical_representative_id" type="row"/>
            </pivot>
        </field>
    </record>

    <record id="action_medical_representative_visit" model="ir.actions.act_window">
        <field name="name">MR Visit</field>
        <field name="res_model">acs.mr.visit</field>
        <field name="view_mode">tree,form,calendar,pivot</field>
        <field name="search_view_id" ref="view_medical_representative_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Click to add a Medical Representative Visit.
            </p>
        </field>
    </record>

</odoo>