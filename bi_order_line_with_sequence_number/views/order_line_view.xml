<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="sale_order_inherit_view" model="ir.ui.view">
        <field name="name">sale.order.form.inherit</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
        	<xpath expr="//field[@name='order_line']/tree/field[@name='sequence']" position="before" >
                <field name="sale_sequence_number" groups="bi_order_line_with_sequence_number.access_display_order_line_number"/>
            </xpath>
        </field>
    </record>

    <record id="purchase_order_inherit_view" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='sequence']" position="before">
                    <field name="purchase_sequence_number" groups="bi_order_line_with_sequence_number.access_display_order_line_number"/>
            </xpath>
        </field>
    </record>

    <record id="purchase_requistion_inherit_view" model="ir.ui.view">
        <field name="name">purchase.requisition.form.inherit</field>
        <field name="model">purchase.requisition</field>
        <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_id']" position="before">
                    <field name="purchase_requistion_sequence" groups="bi_order_line_with_sequence_number.access_display_order_line_number"/>
            </xpath>
        </field>
    </record>
</odoo>