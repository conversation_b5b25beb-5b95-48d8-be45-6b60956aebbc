<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="bi_pos_closed_session_reports_branches">
        <t t-call="web.html_container">
            <div class="page">
                <div class="oe_structure"/>
                <div align="center" style="border:1px solid black;margin:0 auto;padding:0 auto;font-size:18px">
                    <strong>Session Report</strong>
                </div>
                <div align="center" style="margin:0 auto;padding:0 auto;font-size:12px;margin-top:8px;">
                    <t t-if="not o.config_id.branch_id">
                        <div style="font-size:13px;">
                            <t t-if="o.user_id.company_id.logo">
                                <img t-if="o.user_id.company_id.logo"
                                     t-att-src="image_data_uri(o.user_id.company_id.logo)" alt="Logo"
                                     style="width:25%;"/>
                                <br/>
                            </t>
                            <t t-if="o.user_id.company_id.name">
                                <b>
                                    <t t-esc="o.user_id.company_id.name"/>
                                </b>
                                <br/>
                            </t>
                            <t t-if="o.user_id.company_id.street">
                                <t t-esc="o.user_id.company_id.street"/>
                                <br/>
                            </t>
                            <t t-if="o.user_id.company_id.street2">
                                <t t-esc="o.user_id.company_id.street2"/>
                            </t>
                            <t t-if="o.user_id.company_id.city">
                                <t t-esc="o.user_id.company_id.city"/>
                            </t>
                            <t t-if="o.user_id.company_id.state_id.name">
                                <t t-esc="o.user_id.company_id.state_id.name"/>
                            </t>
                            <t t-if="o.user_id.company_id.country_id.name">
                                <t t-esc="o.user_id.company_id.country_id.name"/>
                            </t>
                            <br/>
                            <t t-if="o.user_id.company_id.phone">
                                Tel:
                                <t t-esc="o.user_id.company_id.phone"/>
                                <br/>
                            </t>
                            <t t-if="o.user_id.company_id.email">
                                Email:
                                <t t-esc="o.user_id.company_id.email"/>
                                <br/>
                            </t>
                            <t t-if="o.user_id.company_id.website">
                                Website:
                                <t t-esc="o.user_id.company_id.website"/>
                                <br/>
                            </t>
                        </div>
                    </t>
                    <t t-if="o.config_id.branch_id">
                        <div style="font-size:13px;">
                            <t t-if="o.config_id.branch_logo">
                                <img t-if="o.config_id.branch_logo" t-att-src="image_data_uri(o.config_id.branch_logo)"
                                     alt="Logo" style="width:25%;"/>
                                <br/>
                            </t>
                            <t t-if="o.config_id.branch_id">
                                <b>
                                    <t t-esc="o.config_id.branch_id.name"/>
                                </b>
                                <br/>
                            </t>
                            <t t-if="o.config_id.branch_id">
                                <t t-esc="o.config_id.branch_full_address"/>
                                <br/>
                            </t>
                            <br/>
                            <t t-if="o.config_id.branch_id">
                                Tel:
                                <t t-esc="o.config_id.branch_phone"/>
                                <br/>
                            </t>
                            <t t-if="o.config_id.branch_id">
                                Email:
                                <t t-esc="o.config_id.branch_email"/>
                                <br/>
                            </t>
                            <t t-if="o.config_id.branch_id">
                                Website:
                                <t t-esc="o.config_id.branch_website"/>
                                <br/>
                            </t>
                        </div>
                    </t>
                </div>
                <table style="width:100%; margin-top:15px;">
                    <tr>
                        <td>
                            REPORT ON
                        </td>
                        <td align="right">
                            <span t-esc="o.get_current_datetime()"/>
                        </td>
                    </tr>
                </table>
                <table style="width:100%;">
                    <tr>
                        <td>
                            Sales Person
                        </td>
                        <td align="right">
                            <span t-esc="o.cashier_id.name"/>
                        </td>
                    </tr>
                </table>
                <table style="width:100%;margin-top:15px;">
                    <tr>
                        <td class="text-left">
                            Session
                        </td>
                        <td align="right">
                            <span t-field="o.name"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Opened Date
                        </td>
                       <td align="right">
                           <span t-esc="o.get_opened_date()"/>
                         </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Closed Date
                        </td>
                        <td align="right">
                            <span t-esc="o.get_closed_date()"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Session Status
                        </td>
                        <td align="right">
                            <span t-field="o.state"/>
                        </td>
                    </tr>
                </table>
                <table style="width:100%;margin-top:15px;">
                    <tr>
                        <td class="text-left">
                            Actual Opening
                        </td>
                        <td align="right">
                            <span t-if="o.config_id.open_balance==False" t-esc="o.actual_opening" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                            <span t-if='o.config_id.open_balance' t-esc="o.config_id.open_balance_val" t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Opening Balance
                        </td>
                        <td align="right">
                            <span t-esc="o.cash_register_balance_start"
                                  t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Cash IN
                        </td>
                        <td align="right">
                            <span t-esc="o.get_session_amount_data().get('cash_in', 0)"
                                  t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Cash Out
                        </td>
                        <td align="right">
                            <span t-esc="o.get_session_amount_data().get('cash_out', 0)"
                                  t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Closing Balance
                        </td>
                        <td align="right">
                            <span t-esc="o.cash_register_balance_end_real"
                                  t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Difference
                        </td>
                        <t t-if="o.state != 'closed'">
                            <td align="right">
                                <span t-esc="o.cash_register_difference"
                                      t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                            </td>
                        </t>
                        <t t-if="o.state == 'closed'">
                            <td align="right">
                                <span t-esc="o.cash_real_difference"
                                      t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                            </td>
                        </t>


                    </tr>


                    <tr>
                        <td class="text-left">
                            Gross Sales
                        </td>
                        <td align="right">
                            <span t-esc="o.get_session_amount_data().get('total_sale', 0)"
                                  t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Discount Amount
                        </td>
                        <td align="right">
                            <span t-esc="o.get_session_amount_data().get('discount', 0)"
                                  t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-left">
                            Total
                        </td>
                        <td align="right">
                            <span t-esc="o.get_session_amount_data().get('final_total', 0)"
                                  t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                        </td>
                    </tr>
                </table>
                <t t-if="o.get_session_amount_data().get('products_sold')">
                    <t t-set="product_total" t-value="0"/>
                    <div align="center" style="margin-top:10px !important;">

                        <strong>Categories Wise Sales</strong>

                    </div>
                    <table style="width:100%;margin-top:15px;">
                        <tr>
                            <td style="width:80%; border-bottom:1pt dotted black;">
                                Category
                            </td>
                            <td align="right" style="border-bottom:1pt dotted black;">
                                Quantities
                            </td>
                        </tr>
                        <tr t-foreach="o.get_session_amount_data().get('products_sold')" t-as="l">
                            <t t-set="product_total" t-value="product_total + l_value"/>
                            <td class="text-left">
                                <t t-if="l == 'undefine'">
                                    Others
                                </t>
                                <t t-else="">
                                    <span t-esc="l"/>
                                </t>

                            </td>
                            <td align="right">
                                <span t-esc="l_value"/>
                            </td>
                        </tr>
                    </table>
                    <table style="width:100%;">
                        <tr>
                            <td style="width:50%;">
                            </td>
                            <td style="width:50%; !important" align="right">
                                -------****-------
                            </td>
                        </tr>
                    </table>
                    <table style="width:100%">
                        <tr>
                            <td style="width:50%;" class="text-left">
                                Total Items
                            </td>
                            <td style="width:50%; !important" align="right">
                                <span t-esc="product_total"/>
                            </td>
                        </tr>
                    </table>
                </t>
                <t t-if="o.get_pricelist()">
                    <t t-set="pricelist_total" t-value="0"/>
                    <t t-set="pricelist_qty_total" t-value="0"/>
                    <div align="center" style="margin-top:10px !important;">

                        <strong>Pricelist details</strong>

                    </div>
                    <table style="width:100%;margin-top:15px;">
                        <tr>
                            <td style="width:50%; border-bottom:1pt dotted black;">
                                Pricelist
                            </td>
                            <td align="center" style="width:20%; border-bottom:1pt dotted black;">
                                Quantity
                            </td>
                            <td align="right" style="width:30%; border-bottom:1pt dotted black;">
                                Rising
                            </td>
                        </tr>
                        <tr t-foreach="o.get_pricelist()" t-as="pl">
                            <t t-set="pricelist_total" t-value="pricelist_total + pl_value"/>
                            <td class="text-left">
                                <t t-if="pl == 'undefine'">
                                    Others
                                </t>
                                <t t-else="">
                                    <span t-esc="pl"/>
                                </t>
                            </td>
                            <td align="center">
                                <span t-esc="o.get_pricelist_qty(pl)"/>
                                <t t-set="pricelist_qty_total" t-value="pricelist_qty_total + o.get_pricelist_qty(pl)"/>
                            </td>
                            <td align="right">
                                <span t-esc="pl_value"
                                      t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                            </td>
                        </tr>
                    </table>
                    <table style="width:100%;">
                        <tr>
                            <td style="width:50%;"></td>
                            <td align="right" style="width:50%;">
                                -------------------------------
                            </td>
                        </tr>
                    </table>
                    <table style="width:100%">
                        <tr>
                            <td style="width:50%;" class="text-left">
                                Total
                            </td>
                            <td align="center" style="width:20%;">
                                <span t-esc="pricelist_qty_total"/>
                            </td>
                            <td align="right" style="width:30%;">
                                <span t-esc="pricelist_total"
                                      t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                            </td>
                        </tr>
                    </table>
                </t>
                <t t-if="o.get_payment_data()">
                    <div align="center" style="margin-top:10px !important;">

                        <strong>Payment details</strong>
                    </div>
                    <table style="width:100%;margin-top:15px;">
                        <t t-set="total" t-value="0"/>
                        <t t-set="qty_payment_method" t-value="0"/>
                        <tr>
                            <td style="width:50%; border-bottom:1pt dotted black;">
                                Method
                            </td>
                            <td align="right" style="width:30%; border-bottom:1pt dotted black;">
                                Rising
                            </td>
                        </tr>
                        <tr t-foreach="o.get_payment_data()" t-as="l">
                            <td class="text-left">
                                <span t-esc="l.get('name')"/>
                            </td>
                            <td align="right">
                                <span t-esc="l.get('total')"
                                      t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                                <t t-set="total" t-value="total + l.get('total')"/>
                            </td>
                        </tr>
                    </table>
                    <table style="width:100%;">
                        <tr>
                            <td style="width:50%;"></td>
                            <td align="right" style="width:50%;">
                                -------------------------------
                            </td>
                        </tr>
                    </table>
                    <table style="width:100%">
                        <tr>
                            <td style="width:50%;" class="text-left">
                                Total
                            </td>
                            <td align="right" style="width:30%;">
                                <span t-esc="total"
                                      t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                            </td>
                        </tr>
                    </table>
                </t>
                <br/>
                <br/>
                <center>* * * * *</center>
            </div>
        </t>
    </template>
    <template id="bi_pos_closed_session_reports_branches_report"
              inherit_id="bi_pos_closed_session_reports.report_pos_session_z">
        <xpath expr="//t[@t-call='bi_pos_closed_session_reports.pos_z_report_pdf']" position="replace">
            <t t-call="closed_session_reports_multi_branch.bi_pos_closed_session_reports_branches"/>
        </xpath>
    </template>
</odoo>
