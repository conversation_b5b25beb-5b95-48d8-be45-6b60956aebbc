# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_dropshipping
# 
# Translators:
# <PERSON>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-01-24 08:20+0000\n"
"PO-Revision-Date: 2021-09-14 12:24+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_res_company
msgid "Companies"
msgstr "الشركات "

#. module: mrp_subcontracting_dropshipping
#: code:addons/mrp_subcontracting_dropshipping/models/stock_warehouse.py:0
#: model:stock.location.route,name:mrp_subcontracting_dropshipping.route_subcontracting_dropshipping
#, python-format
msgid "Dropship Subcontractor on Order"
msgstr "إحالة الشحن للمتعاقد من الباطن حسب الطلب "

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship Subcontractors"
msgstr "إحالة الشحن للمتعاقدين من الباطن "

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,help:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_to_resupply
msgid "Dropship subcontractors with components"
msgstr "إحالة الشحن للمتعاقدين من الباطن مع المكونات "

#. module: mrp_subcontracting_dropshipping
#: code:addons/mrp_subcontracting_dropshipping/models/purchase.py:0
#, python-format
msgid ""
"It appears some components in this RFQ are not meant for subcontracting. "
"Please create a separate order for these."
msgstr ""
"يبدو أن بعض المكونات في طلب عرض السعر هذا لا تصلح للتعاقد من الباطن. يرجى "
"إنشاء أمر مختلف لها. "

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_purchase_order
msgid "Purchase Order"
msgstr "أمر شراء"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_rule
msgid "Stock Rule"
msgstr "قاعدة المخزون"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model.fields,field_description:mrp_subcontracting_dropshipping.field_stock_warehouse__subcontracting_dropshipping_pull_id
msgid "Subcontracting-Dropshipping MTS Rule"
msgstr "قاعدة MTS لإحالة الشحن للتعاقد من الباطن "

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_picking
msgid "Transfer"
msgstr "الشحنة"

#. module: mrp_subcontracting_dropshipping
#: model:ir.model,name:mrp_subcontracting_dropshipping.model_stock_warehouse
msgid "Warehouse"
msgstr "المستودع "
