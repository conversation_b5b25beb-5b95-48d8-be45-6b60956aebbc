{
    "name": "Hide Replenishment from Product Info",
    "summary": "This module allows you to hide replenishment from product info button",
    'author': 'Ebtsam@VitaCare',
    'version': '15.0',
    'category': 'Sales/Point of Sale',
    'description': """This module allows you to hide replenishment info from product info button""",
    'depends': ['point_of_sale'],
    'data':[
        'views/res_users.xml',
        ],
    'assets': {
        'point_of_sale.assets': [
            'pos_hide_replenishment_info_section/static/src/js/models.js',
        ],
        'web.assets_qweb': [
            'pos_hide_replenishment_info_section/static/src/xml/ProductInfoPopup.xml',
        ]
    },
    'installable': True,
    'auto_install': False,
    'application': True,
}