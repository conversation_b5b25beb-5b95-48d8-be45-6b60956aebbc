# -*- coding: utf-8 -*-
# Part of BrowseInfo. See LICENSE file for full copyright and licensing details.

from odoo import fields, models, api, _, tools
from datetime import date, time, datetime
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.exceptions import UserError, ValidationError
import logging
_logger = logging.getLogger(__name__)


class pos_order(models.Model):
	_inherit = 'pos.order'

	@api.model
	def create_from_ui(self, orders, draft=False):
		if orders != None:
			order_ids = []
			for order in orders:
				print('orderBalsam', order)
				if not order['data'].get('server_id'):
					existing_order = False
					order_ids.append(self._process_order(order, draft, existing_order))
				# if 'server_id' in order['data']:
				# 	existing_order = self.env['pos.order'].search(
				# 		['|', ('id', '=', order['data']['server_id']), ('pos_reference', '=', order['data']['name'])],
				# 		limit=1)
				# if (existing_order and existing_order.state == 'draft') or not existing_order:
				# 	order_ids.append(self._process_order(order, draft, existing_order))
			return self.env['pos.order'].search_read(domain=[('id', 'in', order_ids)], fields=['id', 'pos_reference'])


