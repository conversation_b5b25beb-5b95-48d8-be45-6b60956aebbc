odoo.define("pos_increase_timeout_for_balsam.Models", function (require) {
    "use strict";

    var models = require("point_of_sale.models");
    var DB = require("point_of_sale.DB");
    var exports = {};

    models.PosModel = models.PosModel.extend({
		/*load the server in backend*/
		_save_to_server: function (orders, options) {
            debugger;
            console.log("saraaaaaaaaa")
            if (!orders || !orders.length) {
                return Promise.resolve([]);
            }

            options = options || {};

            var self = this;
            var timeout = typeof options.timeout === 'number' ? options.timeout : 300000000000 * orders.length;

            // Keep the order ids that are about to be sent to the
            // backend. In between create_from_ui and the success callback
            // new orders may have been added to it.
            var order_ids_to_sync = _.pluck(orders, 'id');

            // we try to send the order. shadow prevents a spinner if it takes too long. (unless we are sending an invoice,
            // then we want to notify the user that we are waiting on something )
            var args = [_.map(orders, function (order) {
//                    order.to_invoice = options.to_invoice || false;
                    return order;
                })];
            args.push(options.draft || false);
            return this.rpc({
                    model: 'pos.order',
                    method: 'create_from_ui',
                    args: args,
                    kwargs: {context: this.session.user_context},
                }, {
                    timeout: timeout,
                    shadow: !options.to_invoice
                })
                .then(function (server_ids) {
                    _.each(order_ids_to_sync, function (order_id) {
                        self.db.remove_order(order_id);
                    });
                    self.set('failed',false);
                    return server_ids;
                }).catch(function (error){
                    console.warn('Failed to send orders:', orders);
                    if(error.code === 200 ){    // Business Logic Error, not a connection problem
                        // Hide error if already shown before ...
                        if ((!self.get('failed') || options.show_error) && !options.to_invoice) {
                            self.set('failed',error);
                            throw error;
                        }
                    }
                    throw error;
                });
        },
        _after_flush_orders: function(orders) {
            const refundedOrderIds = new Set();
    //        for (const order of orders) {
    //            for (const line of order.data.lines) {
    //                const refundDetail = this.toRefundLines[line[2].refunded_orderline_id];
    //                if (!refundDetail) continue;
    //                // Collect the backend id of the refunded orders.
    //                refundedOrderIds.add(refundDetail.orderline.orderBackendId);
    //                // Reset the refund detail for the orderline.
    //                delete this.toRefundLines[refundDetail.orderline.id];
    //            }
    //        }
        this._invalidateSyncedOrdersCache([...refundedOrderIds]);
    },
	});

});
