# -*- coding: utf-8 -*-
from distutils.command.build_scripts import first_line_re

from odoo import models, fields, api

class ProductPurchasetData(models.Model):
    _name = 'product.purchase.data'
    _order = 'name'

    date_approve=fields.Date('Confirmation Date')
    name=fields.Char('Reference')
    partner_id=fields.Many2one('res.partner',string='Vendor')
    product_id=fields.Many2one('product.product',string='Product')
    product_qty=fields.Float('Quantity')
    bonus_qty=fields.Float('Bonus Qty')
    price_unit=fields.Float('Purchase Price')
    discount=fields.Float('Discount %')
    multi_discount=fields.Char('Discounts')
    discount_amount_plus=fields.Float('Discount(Amount)')
    profit_margin=fields.Float('Profit%')
    sale_price_ex_tax = fields.Float('Sale Ex.Tax',)
    product_sales_price=fields.Float('Sales Price')
    price_tax=fields.Float('Tax')
    taxes_id=fields.Many2many('account.tax',string='Taxes')
    branch_id = fields.Many2one('res.branch', string='Branch',)
    picking_type_id=fields.Many2one('stock.picking.type',string='warehouse')
    location_id=fields.Many2one('stock.location',string='Location')
    price_subtotal=fields.Float('Subtotal')
    promo_percentage = fields.Float('Promo %')


class PurchaseOrder(models.Model):
    _inherit = "purchase.order"


    confirm_date_date=fields.Date(compute='get_confirm_date',store=1)

    @api.depends('date_approve')
    def get_confirm_date(self):
        for rec in self:
            if rec.date_approve:
                rec.confirm_date_date=rec.date_approve.date()