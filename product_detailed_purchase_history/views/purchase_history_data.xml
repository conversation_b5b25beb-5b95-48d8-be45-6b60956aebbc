<odoo>
    <data>

            <record id="purchase_report_tree_view" model="ir.ui.view">
        <field name="name">product.purchase.data.tree.view</field>
        <field name="model">product.purchase.data</field>
        <field name="arch" type="xml">
            <tree create="0" edit="0">
                <field name="date_approve"/>
                <field name="name"/>
                <field name="partner_id"/>
                <field name="product_id"/>
                <field name="branch_id" optional="hide"/>
                 <field name="picking_type_id" optional="hide"/>
                <field name="location_id" optional="hide"/>
                <field name="product_qty"/>
                <field name="bonus_qty"/>
                <field name="price_unit"/>
                  <field name="product_sales_price" optional="hide"/>
                <field name="price_subtotal" optional="hide"/>
                <field name="sale_price_ex_tax" optional="hide"/>
                <field name="taxes_id" widget="many2many_tags" optional="hide"/>
                <field name="price_tax" optional="hide"/>
                 <field name="discount"/>
                <field name="multi_discount"/>
                <field name="discount_amount_plus"/>
                <field name="profit_margin"/>
                  <field name="promo_percentage" optional="hide"/>
            </tree>
        </field>
    </record>

        <record id="purchase_report_search" model="ir.ui.view">
        <field name="name">product.purchase.data.search.view.filter</field>
        <field name="model">product.purchase.data</field>
        <field name="arch" type="xml">
            <search string="Search  Report">
                <field name="date_approve"/>
                <field name="name"/>
                <field name="partner_id"/>
                <field name="product_id"/>
                <field name="product_qty"/>
                <field name="bonus_qty"/>
                <field name="price_unit"/>
                <field name="discount"/>
                <field name="multi_discount"/>
                <field name="discount_amount_plus"/>
                <field name="profit_margin"/>
                <separator/>
                <group expand="0" string="Group By">
                    <filter string="Vendor" name="partner_id"
                            context="{'group_by': 'partner_id'}"/>
                    <filter string="Product" name="group_by_page"  domain="[]"  context="{'group_by': 'product_id'}"/>

                </group>
            </search>
        </field>
    </record>
 <record id="view_purchase_history_pivot" model="ir.ui.view">
        <field name="name">product.purchase.datat.pivot</field>
        <field name="model">product.purchase.data</field>
        <field name="arch" type="xml">
            <pivot string="Purchase Analysis" disable_linking="True">
                <field name="partner_id" type="col"/>
                <field name="product_id"/>
                <field name="product_qty" type="measure"/>
                 <field name="bonus_qty" type="measure"/>

            </pivot>
        </field>
    </record>

    </data>
</odoo>