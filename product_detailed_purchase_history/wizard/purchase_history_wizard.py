# -*- coding: utf-8 -*-
from odoo import api, fields, models, tools, _
from odoo.exceptions import ValidationError, UserError



class CentricProductWizard(models.TransientModel):
    _name = "purchase.history.wizard"

    date_from=fields.Date()
    date_to=fields.Date()
    vendor_ids=fields.Many2many('res.partner','vendors_rel','col1','col2',string='Vendors')
    product_ids = fields.Many2many('product.product', 'product_rel', 'col3', 'col4', string='Products')
    data_type = fields.Selection([('all', 'All Purchase Order'), ('last', 'Last Purchase order Item/Vendor')], default='last',required=1)


    def get_all_data_type(self):
        domain = [('state', '=', 'purchase'),('company_id', '=', self.env.company.id)]
        data = []
        if self.vendor_ids:
            domain.append(('partner_id', 'in', self.vendor_ids.ids))
        if self.product_ids:
            domain.append(('product_id', 'in', self.product_ids.ids))
        if self.date_from:
            domain.append(('order_id.confirm_date_date', '>=', self.date_from))
        if self.date_to:
            domain.append(('order_id.confirm_date_date', '<=', self.date_to))

        purchase_data = self.env['purchase.order.line'].search(domain)
        if  purchase_data:

           for line in purchase_data:
               data.append({
                   'date_approve': line.order_id.confirm_date_date,
                   'product_id': line.product_id.id,
                   'partner_id': line.partner_id.id,
                   'name': line.order_id.name,
                   'branch_id': line.order_id.branch_id.id,
                   'picking_type_id': line.order_id.picking_type_id.id,
                   'location_id': line.order_id.picking_ids[0].location_dest_id.id,
                   'product_qty': line.product_qty,
                   'bonus_qty': line.bonus_qty,
                   'price_unit': line.price_unit,
                   'product_sales_price': line.product_sales_price,
                   'discount': line.discount,
                   'multi_discount': line.multi_discount,
                   'price_subtotal': line.price_subtotal,
                   'promo_percentage': line.promo_percentage,
                   'discount_amount_plus': line.discount_amount_plus,
                   'profit_margin': line.profit_margin*100,
                   'sale_price_ex_tax': line.sale_price_ex_tax,
                   'price_tax': line.price_tax,
                   'taxes_id': [(6, 0, line.taxes_id.ids)],




               })
           history_data = self.env['product.purchase.data'].create(data)



    def get_last_data(self):
        domain = [('state', '=', 'purchase'),('company_id', '=', self.env.company.id)]

        if self.product_ids:
            domain.append(('product_id', 'in', self.product_ids.ids))
        if self.date_from:
            domain.append(('order_id.confirm_date_date', '>=', self.date_from))
        if self.date_to:
            domain.append(('order_id.confirm_date_date', '<=', self.date_to))
        if self.vendor_ids:
            domain.append(('partner_id', 'in', self.vendor_ids.ids))

        purchase_data = self.env['purchase.order.line'].search(domain)

        last_orders = {}
        for line in purchase_data:
            product_id = line.product_id.id
            partner_id = line.partner_id.id
            confirm_date = line.order_id.confirm_date_date
            order_id = line.order_id.id
            if not self.product_ids and not self.vendor_ids:
                key = (product_id, partner_id)
            elif self.product_ids and not self.vendor_ids:
                key = (product_id, partner_id)
            elif self.product_ids and self.vendor_ids:
                key = (product_id, partner_id)
            elif self.vendor_ids and not self.product_ids:
                key = (product_id, partner_id)
            else:
                key = None

            if key:

                if key not in last_orders or confirm_date > last_orders[key]['confirm_date']:
                    last_orders[key] = {
                        'order_id': order_id,
                        'confirm_date': confirm_date,
                        'lines': [line]}
                elif confirm_date == last_orders[key]['confirm_date']:
                    continue

        data = []
        for key, order_data in last_orders.items():
            lines = order_data['lines']
            for line in lines:
                order = line.order_id
                data.append({
                    'date_approve': order.confirm_date_date,
                    'product_id': line.product_id.id,
                    'partner_id': line.partner_id.id,
                    'name': order.name,
                    'branch_id':order.branch_id.id,
                    'picking_type_id': order.picking_type_id.id,
                    'product_qty': line.product_qty,
                    'bonus_qty': line.bonus_qty,
                    'price_unit': line.price_unit,
                    'location_id': line.order_id.picking_ids[0].location_dest_id.id,
                    'discount': line.discount,
                    'product_sales_price': line.product_sales_price,
                    'price_subtotal': line.price_subtotal,
                    'promo_percentage': line.promo_percentage,
                    'multi_discount': line.multi_discount,
                    'discount_amount_plus': line.discount_amount_plus,
                    'profit_margin': line.profit_margin*100,
                    'sale_price_ex_tax': line.sale_price_ex_tax,
                    'price_tax': line.price_tax,
                    'taxes_id': [(6, 0, line.taxes_id.ids)],

                })

        history_data = self.env['product.purchase.data'].create(data)

    def get_data(self):
        purchase_data = self.env['product.purchase.data']
        purchase_data.search([]).unlink()
        if  self.data_type == 'all':
            self.get_all_data_type()
        else:
           self.get_last_data()

        action = {'name': _('Product Detailed Purchase History Report'),
                  'view_mode': 'list,graph,pivot',
                  'res_model': 'product.purchase.data',
                  'type': 'ir.actions.act_window', 'target': 'current',
                  'context': {'search_default_group_by_page': '1'}

                  }

        return action




