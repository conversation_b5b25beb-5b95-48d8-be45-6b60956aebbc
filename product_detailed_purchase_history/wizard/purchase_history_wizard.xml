<odoo>
    <data>

        <record id="purchase_wizard_wizard_view_form" model="ir.ui.view">
        <field name="name">purchase_history.wizard.form</field>
        <field name="model">purchase.history.wizard</field>
        <field name="arch" type="xml">
            <form string="Product Detailed Purchase History">

                <group>
                    <group>
                        <field name="data_type"/>
                    </group>
                     <group>
                        <field name="date_from"/>
                        <field name="date_to"/>
                    </group>
                </group>

<!--                <group>-->
<!--                   -->
<!--                </group>-->
                <group>
                    <group>
                        <field name="vendor_ids" widget="many2many_tags"  options="{'no_create':True}" attrs="{'required':[('data_type','!=','last')]}"/>
                        <field name="product_ids" widget="many2many_tags" options="{'no_create':True}"/>
                    </group>

                </group>
                <footer>
                    <button string="View" name="get_data" type="object" class="btn-primary"/>
                    <button string="Close" class="btn-default" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>


        <record model="ir.actions.act_window" id="action_product_detailed_wizard">
        <field name="name">Product Detailed Purchase Report </field>
        <field name="res_model">purchase.history.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <menuitem id="product_purchase_report" name="Product Detailed Purchase"
              parent="purchase.purchase_report_main" sequence="100"
              action="action_product_detailed_wizard"/>

        <record id="purchase_order_line_action" model="ir.actions.act_window">
            <field name="name">Purchase Order Lines</field>
            <field name="type">ir.actions.act_window</field>
            <field name="res_model">purchase.order.line</field>
            <field name="domain">[]</field>
            <field name="view_mode">tree</field>

        </record>

<!--        <menuitem action="purchase_order_line_action" id="purchase_order_line_action_id"-->
<!--            parent="purchase.menu_procurement_management"-->
<!--            sequence="8"/>-->


    </data>
</odoo>