# -*- coding: utf-8 -*-
{
    'name': 'Show Product Arabic Name in Pos Receipt',
    'version': '15.0',
    'author': 'Ebtsam@Vitacare',
    'category': 'Point of Sale',
    'depends': ['point_of_sale', 'pharmacy_management', 'vc_stock_custom'],
    'summary': 'Show Product Arabic Name in Pos Receipt',
    'description': """
        This module allow to Show Product Arabic Name or English Name or both in Pos Receipt Depend on Session Settings
    """,
    'data': [
        'views/pos_config_view.xml',
        'views/barcode_label_template_inherit.xml',
    ],
    'assets': {
        'point_of_sale.assets': [
            '/show_product_arabic_name_in_pos_receipt/static/src/js/ProductArabicName.js',
        ],
        'web.assets_qweb': [
            'show_product_arabic_name_in_pos_receipt/static/src/xml/**/*',
        ],
    },

    'application': True,
    'installable': True,

}
