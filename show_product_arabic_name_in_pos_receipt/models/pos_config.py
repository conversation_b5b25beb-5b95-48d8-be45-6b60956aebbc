# -*- coding: utf-8 -*-
from odoo import api, fields, models, _


class PosConfig(models.Model):
    _inherit = 'pos.config'

    product_language_type = fields.Selection([
        ('show_arabic_name_receipt', 'Show Product Arabic Name in Pos Receipt'),
        ('show_english_name_receipt', 'Show Product English Name in Pos Receipt'),
        ('show_arabic_english_name_receipt', 'Show Both Product Arabic and English Name in Pos Receipt')],
        string='Product Language Type', default="show_english_name_receipt", required=True, store=True)

