odoo.define('show_product_arabic_name_in_pos_receipt.ArabicName', function (require) {
    'use strict';

    const ClosePosPopup = require('point_of_sale.ClosePosPopup');
    const Registries = require('point_of_sale.Registries');
    var pos_model = require('point_of_sale.models');
	var SuperOrderline = pos_model.Orderline.prototype;
    pos_model.load_fields("product.product", ['arabic_name', 'original_name']);

    pos_model.Orderline = pos_model.Orderline.extend({
		export_for_printing: function(){
			var dict = SuperOrderline.export_for_printing.call(this);
			dict.arabic_name = this.get_product().arabic_name;
			dict.original_name = this.get_product().original_name;
			dict.is_promotion_line = Boolean(this.promotion_id);
			return dict;
		},
	});


});
