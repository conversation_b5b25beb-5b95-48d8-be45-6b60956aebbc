<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
<t t-name="WrappedProductNameLinesvita" t-inherit="point_of_sale.WrappedProductNameLines" t-inherit-mode="extension" owl="1">
    <xpath expr="//span" position="replace">
        <span>
<!--            <t t-foreach="props.line.product_name_wrapped.slice(1)" t-as="wrapped_line"><t t-esc="wrapped_line"/></t>-->
        </span>
    </xpath>
</t>
 <t t-name="OrderLinesReceipttt" t-inherit="point_of_sale.OrderLinesReceipt" t-inherit-mode="extension" owl="1">
      <!--Override-->
<!--    <xpath expr="//t[@t-foreach='receipt.orderlines']" position="replace">-->
<!--        <t t-foreach="receipt.orderlines" t-as="line" t-key="line.id">-->
<!--            <t t-if="isSimple(line)" t-attf-style='position:relative;bottom:60px;'>-->
<!--                <t t-if="env.pos.config.product_language_type === 'show_english_name_receipt' || env.pos.config.product_language_type === 'show_arabic_english_name_receipt'">-->
<!--                    <div style="font-weight:bold;"><t t-esc="line.original_name" /></div>-->
<!--                </t>-->
<!--                <t t-if="env.pos.config.product_language_type === 'show_arabic_name_receipt' || env.pos.config.product_language_type === 'show_arabic_english_name_receipt'">-->
<!--                    <div style="font-weight:bold;"><t t-esc="line.arabic_name" /></div>-->
<!--                </t>-->
<!--&lt;!&ndash;                    <t t-if="line.string_line">&ndash;&gt;-->
<!--&lt;!&ndash;                            -<t t-esc="line.string_line" class="pos-disc-font"/>&ndash;&gt;-->
<!--&lt;!&ndash;                    </t>&ndash;&gt;-->
<!--                <t t-if="env.pos.format_currency_no_symbol(line.price_display) == 0 || line.is_promotion_line">-->
<!--                        <div style="display:inline-block;background: #099a52;color:white;margin: 0px auto 0px auto;padding:5px 10px;border-radius: 15px;">-->
<!--                            Promotion Product-->
<!--                        </div>-->
<!--                </t>-->
<!--&lt;!&ndash;                </t>&ndash;&gt;-->

<!--                <div class="responsive-price">-->
<!--                    <span t-esc="env.pos.format_currency_no_symbol(line.price_display)" class="price_display pos-receipt-right-align"/>-->
<!--                </div>-->
<!--                <WrappedProductNameLines line="line" />-->
<!--            </t>-->
<!--            <t t-else="" t-attf-style='position:relative;bottom:60px;'>-->
<!--                <t t-if="env.pos.config.product_language_type === 'show_english_name_receipt' || env.pos.config.product_language_type === 'show_arabic_english_name_receipt'">-->
<!--                    <div style="font-weight:bold;"><t t-esc="line.original_name" /></div>-->
<!--                </t>-->
<!--                <t t-if="env.pos.config.product_language_type === 'show_arabic_name_receipt' || env.pos.config.product_language_type === 'show_arabic_english_name_receipt'">-->
<!--                    <div style="font-weight:bold;"><t t-esc="line.arabic_name" /></div>-->
<!--                </t>-->
<!--                <t t-if="env.pos.format_currency_no_symbol(line.price_display) == 0 || line.is_promotion_line">-->
<!--                        <div style="display:inline-block;background: #099a52;color:white;margin: 0px auto 0px auto;padding:5px 10px;border-radius: 15px;">-->
<!--                            Promotion Product-->
<!--                        </div>-->
<!--                </t>-->
<!--&lt;!&ndash;                    <t t-if="line.string_line">&ndash;&gt;-->
<!--&lt;!&ndash;                            -<t t-esc="line.string_line" class="pos-disc-font"/>&ndash;&gt;-->
<!--&lt;!&ndash;                    </t>&ndash;&gt;-->
<!--&lt;!&ndash;                </t>&ndash;&gt;-->


<!--                -->
<!--                <WrappedProductNameLines line="line" />-->
<!--                <t t-if="line.display_discount_policy == 'without_discount' and line.price != line.price_lst">-->
<!--                    <div class="pos-receipt-left-padding">-->
<!--                        Price BF.Disc:<t t-esc="env.pos.format_currency_no_symbol(line.price_lst)" />-->
<!--                        ->-->
<!--                        Price BF.Disc:-->
<!--                        <t t-esc="env.pos.format_currency_no_symbol(line.price)" />-->
<!--                    </div>-->
<!--                </t>-->
<!--                <t t-elif="line.discount !== 0">-->
<!--                    <div style="font-weight:bold;" class="pos-receipt-left-padding">-->
<!--                        <t t-if="env.pos.config.iface_tax_included === 'total'">-->
<!--                           Price BF.Disc:<t t-esc="env.pos.format_currency_no_symbol(line.price_with_tax_before_discount)"/>-->
<!--                        </t>-->
<!--                        <t t-else="">-->
<!--                            Price BF.Disc:<t t-esc="env.pos.format_currency_no_symbol(line.price)"/>-->
<!--                        </t>-->
<!--                    </div>-->
<!--                </t>-->
<!--                <t t-if="line.discount !== 0">-->
<!--                    <div style="font-weight:bold;" class="pos-receipt-left-padding">-->
<!--                        Discount: <t t-esc="line.discount" />%-->
<!--                       <span style="padding-left:45px;">Price AF.Disc:</span>-->

<!--                    </div>-->
<!--                </t>-->
<!--                <div class="pos-receipt-left-padding">-->
<!--                    <t t-esc="Math.round(line.quantity * Math.pow(10, env.pos.dp['Product Unit of Measure'])) / Math.pow(10, env.pos.dp['Product Unit of Measure'])"/>-->
<!--                    <t t-if="!line.is_in_unit" t-esc="line.unit_name" />-->
<!--                    x-->
<!--                    <t t-esc="env.pos.format_currency(line.get_unit_display_price_qty_one())" />-->
<!--                    <span class="price_display pos-receipt-right-align">-->
<!--                        <t t-esc="env.pos.format_currency_no_symbol(line.price_display)" />-->
<!--                    </span>-->
<!--                </div>-->
<!--            </t>-->
<!--            <t t-if="line.customer_note">-->
<!--                <div class="pos-receipt-left-padding pos-receipt-customer-note">-->
<!--                    <t t-esc="line.customer_note"/>-->
<!--                </div>-->
<!--            </t>-->
<!--            <t t-if="line.pack_lot_lines">-->
<!--                <div class="pos-receipt-left-padding">-->
<!--                    <ul>-->
<!--                        <t t-foreach="line.pack_lot_lines" t-as="lot" t-key="lot.cid">-->
<!--                            <li>-->
<!--                                SN <t t-esc="lot.attributes['lot_name']"/>-->
<!--                            </li>-->
<!--                        </t>-->
<!--                    </ul>-->
<!--                </div>-->
<!--            </t>-->
<!--        </t>-->

<!--    </xpath>-->



    </t>

</templates>
