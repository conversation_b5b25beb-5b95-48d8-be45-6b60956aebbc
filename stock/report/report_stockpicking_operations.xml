<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <template id="report_picking">
            <t t-call="web.html_container">
                <t t-foreach="docs" t-as="o">
                    <t t-call="web.external_layout">
                        <div class="page">
                            <div class="row justify-content-end mb16">
                                <div class="col-4" name="right_box">
                                    <div t-field="o.name" t-options="{'widget': 'barcode', 'width': 600, 'height': 100, 'img_style': 'width:300px;height:50px;'}"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6" name="div_outgoing_address">
                                    <div t-if="o.should_print_delivery_address()">
                                        <span><strong>Delivery Address:</strong></span>
                                        <div t-field="o.move_lines[0].partner_id"
                                            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                                    </div>
                                    <div t-elif="o.picking_type_id.code != 'internal' and o.picking_type_id.warehouse_id.partner_id">
                                        <span><strong>Warehouse Address:</strong></span>
                                        <div t-field="o.picking_type_id.warehouse_id.partner_id"
                                            t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                                    </div>
                                </div>
                                <div class="col-5 offset-1" name="div_incoming_address">
                                    <t t-set="show_partner" t-value="False" />
                                    <div t-if="o.picking_type_id.code=='incoming' and o.partner_id">
                                        <span><strong>Vendor Address:</strong></span>
                                        <t t-set="show_partner" t-value="True" />
                                    </div>
                                    <div t-if="o.picking_type_id.code=='internal' and o.partner_id">
                                        <span><strong>Warehouse Address:</strong></span>
                                        <t t-set="show_partner" t-value="True" />
                                    </div>
                                    <div t-if="o.picking_type_id.code=='outgoing' and o.partner_id and o.partner_id != o.partner_id.commercial_partner_id">
                                        <span><strong>Customer Address:</strong></span>
                                        <t t-set="show_partner" t-value="True" />
                                    </div>
                                    <div t-if="show_partner" name="partner_header">
                                        <div t-field="o.partner_id.commercial_partner_id"
                                           t-options='{"widget": "contact", "fields": ["address", "name", "phone"], "no_marker": True, "phone_icons": True}'/>
                                        <p t-if="o.sudo().partner_id.commercial_partner_id.vat"><t t-esc="o.company_id.country_id.vat_label or 'Tax ID'"/>: <span t-field="o.sudo().partner_id.commercial_partner_id.vat"/></p>
                                    </div>
                                </div>
                            </div>
                            <br/>
                            <h1 t-field="o.name" class="mt0 float-left"/>
                            <div class="row mt48 mb32">
                                <div t-if="o.origin" class="col-auto" name="div_origin">
                                    <strong>Order:</strong>
                                    <p t-field="o.origin"/>
                                </div>
                                <div class="col-auto" name="div_state">
                                    <strong>Status:</strong>
                                    <p t-field="o.state"/>
                                </div>
                                <div class="col-auto" name="div_sched_date">
                                    <strong>Scheduled Date:</strong>
                                    <p t-field="o.scheduled_date"/>
                                </div>
                            </div>
                            <table class="table table-sm" t-if="o.move_line_ids and o.move_ids_without_package">
                                <t t-set="has_barcode" t-value="any(move_line.product_id and move_line.product_id.sudo().barcode or move_line.package_id for move_line in o.move_line_ids)"/>
                                <t t-set="has_serial_number" t-value="any(move_line.lot_id or move_line.lot_name for move_line in o.move_line_ids)" groups="stock.group_production_lot"/>
                                <thead>
                                    <tr>
                                        <th name="th_seq" class="text-center">
                                            <strong>Seq</strong>
                                        </th>
                                        <th name="th_product">
                                            <strong>Product</strong>
                                        </th>
                                        <th>
                                            <strong>Quantity</strong>
                                        </th>
                                        <th name="th_from" t-if="o.picking_type_id.code != 'incoming'" align="left" groups="stock.group_stock_multi_locations">
                                            <strong>From</strong>
                                        </th>
                                        <th name="th_to" t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">
                                            <strong>To</strong>
                                        </th>
                                        <th name="th_serial_number" class="text-center" t-if="has_serial_number">
                                           <strong>Lot/Serial Number</strong>
                                        </th>
                                        <th name="th_barcode" class="text-center" t-if="has_barcode">
                                            <strong>Product Barcode</strong>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- In case you come across duplicated lines, ask NIM or LAP -->
                                    <t t-foreach="o.move_line_ids_without_package" t-as="ml">
                                        <tr>
                                            <td class="text-center">
                                                <span t-esc="ml_index + 1"/>
                                            </td>
                                            <td>
                                                <span t-field="ml.product_id.display_name"/><br/>
                                                <span t-field="ml.product_id.description_picking"/>
                                            </td>
                                            <td>
                                                <span t-if="o.state != 'done'" t-field="ml.product_uom_qty"/>
                                                <span t-if="o.state == 'done'" t-field="ml.qty_done"/>
                                                <span t-field="ml.product_uom_id" groups="uom.group_uom"/>
                                            </td>
                                            <td t-if="o.picking_type_id.code != 'incoming'" groups="stock.group_stock_multi_locations">
                                                <span t-esc="ml.location_id.display_name"/>
                                                    <t t-if="ml.package_id">
                                                        <span t-field="ml.package_id"/>
                                                    </t>
                                            </td>
                                            <td t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">
                                                <div>
                                                    <span t-field="ml.location_dest_id"/>
                                                    <t t-if="ml.result_package_id">
                                                        <span t-field="ml.result_package_id"/>
                                                    </t>
                                                </div>
                                            </td>
                                            <td class=" text-center h6" t-if="has_serial_number">
                                                <div t-if="has_serial_number and (ml.lot_id or ml.lot_name)" t-esc="ml.lot_id.name or ml.lot_name" t-options="{'widget': 'barcode', 'humanreadable': 1, 'width': 400, 'height': 100, 'img_style': 'width:100%;height:35px;'}"/>
                                            </td>
                                            <td class="text-center" t-if="has_barcode">
                                                <t t-if="product_barcode != ml.product_id.barcode">
                                                    <span t-if="ml.product_id and ml.product_id.barcode">
                                                        <div t-field="ml.product_id.barcode" t-options="{'widget': 'barcode', 'symbology': 'auto', 'width': 400, 'height': 100, 'quiet': 0, 'img_style': 'height:35px;'}"/>
                                                    </span>
                                                    <t t-set="product_barcode" t-value="ml.product_id.barcode"/>
                                                </t>
                                            </td>
                                        </tr>
                                    </t>
                                  </tbody>
                            </table>
                            <table class="table table-sm" t-if="o.package_level_ids and o.picking_type_entire_packs and o.state in ['assigned', 'done']">
                                <thead>
                                    <tr>
                                        <th name="th_seq" class="text-center">Seq</th>
                                        <th name="th_package">Package</th>
                                        <th name="th_pko_from" t-if="o.picking_type_id.code != 'incoming'" groups="stock.group_stock_multi_locations">From</th>
                                        <th name="th_pki_from" t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">To</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-foreach="o.package_level_ids.sorted(key=lambda p: p.package_id.name)" t-as="package">
                                        <t t-set="package" t-value="package.with_context(picking_id=o.id)" />
                                        <td class="text-center">
                                            <span t-esc="package_index + 1"/>
                                        </td>
                                        <td name="td_pk_barcode">
                                            <div t-field="package.package_id.name" t-options="{'widget': 'barcode', 'humanreadable': 1, 'width': 600, 'height': 100, 'img_style': 'width:300px;height:50px;margin-left: -50px;'}"/><br/>
                                        </td>
                                        <td t-if="o.picking_type_id.code != 'incoming'" groups="stock.group_stock_multi_locations">
                                            <span t-field="package.location_id"/>
                                        </td>
                                        <td t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">
                                            <span t-field="package.location_dest_id"/>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <t t-set="no_reserved_product" t-value="o.move_lines.filtered(lambda x: x.product_uom_qty != x.reserved_availability and x.move_line_ids and x.state!='done')"/>
                            <p t-if="o.state in ['draft', 'waiting', 'confirmed'] or no_reserved_product"><i class="fa fa-exclamation-triangle" />
                                All products could not be reserved. Click on the "Check Availability" button to try to reserve products.
                            </p>
                            <p t-field="o.note"/>
                        </div>
                    </t>
                </t>
            </t>
        </template>
        <template id="report_picking_type_label">
            <t t-set="title">Operation Types</t>
            <t t-call="stock.report_generic_barcode"/>
        </template>
    </data>
</odoo>
