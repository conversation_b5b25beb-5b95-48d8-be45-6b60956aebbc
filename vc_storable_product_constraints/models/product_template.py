# -*- coding: utf-8 -*-
from odoo.exceptions import ValidationError
from odoo import models, api, _


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    @api.model
    def create(self, values):
        res = super(ProductTemplate, self).create(values)
        if self.env.context.get('import_file'):
            return res
        if res.detailed_type == 'product':
            if not res.taxes_id:
                raise ValidationError(_("Customer Taxes Is Required "))
            if not res.supplier_taxes_id:
                raise ValidationError(_("Vendor Taxes Is Required "))
        return res

    def write(self, values):
        res = super(ProductTemplate, self).write(values)
        if self.env.context.get('import_file'):
            return res
        for record in self:
            if record.detailed_type == 'product':
                if not record.taxes_id:
                    raise ValidationError(_("Customer Taxes Is Required "))
                if not record.supplier_taxes_id:
                    raise ValidationError(_("Vendor Taxes Is Required "))
        return res
