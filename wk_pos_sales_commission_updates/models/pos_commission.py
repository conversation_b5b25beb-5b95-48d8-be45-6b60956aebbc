from odoo import api, fields, models


class PosCommission(models.Model):
    _inherit = 'pos.commission'

    total_flag = fields.Boolean(string='Total Flag', compute='_get_total_flag')

    @api.depends('product_id')
    def _get_total_flag(self):
        for commission in self:
            if commission.product_id.is_commission_product:
                commission.total_flag = True
            else:
                commission.total_flag = False
