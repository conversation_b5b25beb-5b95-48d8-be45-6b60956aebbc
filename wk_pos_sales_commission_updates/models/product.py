from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
#
#
class ProductProduct(models.Model):
    _inherit = 'product.product'

    is_commission_product = fields.Boolean('Is Commission Product')


    @api.constrains('is_commission_product', 'type')
    def _check_commission_product(self):
        for rec in self:
            if rec.type != 'service' and rec.is_commission_product:
                raise ValidationError(
                    _('Only products of type "service" can be commission, this product type is') + f' "{rec.type}"')